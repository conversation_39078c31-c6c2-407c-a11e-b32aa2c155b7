# CMA (Competitive Market Analysis) Platform - Codebase Documentation

## Overview

CMA is an AI-powered Web3 research platform that automatically generates comprehensive Competitive Market Analysis reports from simple inputs like web domains or Twitter handles. The platform leverages multi-agent AI systems, real-time blockchain analytics, and social sentiment analysis to deliver institutional-grade research reports.

## Architecture

### Technology Stack

- **Frontend**: Next.js 15 with App Router, React 19, TypeScript
- **Backend**: Next.js API routes with tRPC for type-safe APIs
- **Database**: PostgreSQL with Prisma ORM and pgvector for embeddings
- **AI/ML**: Vercel AI SDK with OpenAI integration
- **Monorepo**: Turborepo with Bun package manager
- **UI**: Tailwind CSS with shadcn/ui components
- **Real-time**: Supabase for real-time subscriptions and database

### Project Structure

```
CMA/
├── apps/
│   ├── web/                    # Frontend Next.js application (Port 3001)
│   │   ├── src/app/           # App Router pages
│   │   │   ├── dashboard/     # Analysis dashboard
│   │   │   ├── analysis/      # Analysis tracking pages
│   │   │   └── reports/       # Report viewing pages
│   │   ├── src/components/    # Reusable UI components
│   │   └── src/utils/         # Client utilities (tRPC client)
│   │
│   └── server/                # Backend API server (Port 3000)
│       ├── src/app/api/       # Next.js API routes
│       ├── src/routers/       # tRPC routers
│       ├── src/lib/           # Server utilities
│       └── prisma/            # Database schema and migrations
│
├── packages/                  # Shared libraries
│   ├── agents/                # Multi-agent research system
│   ├── ai/                    # AI/LLM integrations
│   └── database/              # Database client and schemas
│
└── Docs/                      # Documentation
    └── PRD.md                 # Product Requirements Document
```

## Core Components

### 1. Multi-Agent Research System (`packages/agents/`)

The heart of the platform is a sophisticated multi-agent orchestration system:

**Agent Orchestrator** (`orchestrator.ts`)
- Coordinates 6+ specialized research agents
- Manages parallel execution and data flow
- Handles error recovery and progress tracking

**Specialized Agents:**
- **Lead Research Agent**: Analyzes input, creates research plan, coordinates subagents
- **Social Sentiment Agent**: Real-time Twitter analysis using X.AI Live Search API
- **Competitor Analysis Agent**: Identifies and analyzes direct competitors
- **Market Positioning Agent**: Determines market position and opportunities

**Data Integrations:**
- **Firecrawl**: Web scraping with JavaScript rendering
- **Exa API**: Neural search for research content discovery
- **Blockchain RPCs**: Direct on-chain data access
- **Social APIs**: Twitter/X data collection

### 2. Frontend Application (`apps/web/`)

**Dashboard** (`src/app/dashboard/page.tsx`)
- Real-time analysis progress tracking
- Agent status monitoring with live updates
- Recent reports overview
- Activity feed with timestamped events

**Key Features:**
- Real-time progress bars for each agent
- Mock simulation system for development/demo
- Responsive design with shadcn/ui components
- tRPC integration for type-safe API calls

### 3. Backend API (`apps/server/`)

**tRPC Router** (`src/routers/index.ts`)
- `startAnalysis`: Initiates multi-agent research process
- `getAnalysisProgress`: Real-time progress tracking
- `getReport`: Retrieves completed analysis reports
- `listReports`: Lists user's historical reports

**Database Integration:**
- Supabase client for real-time subscriptions
- Prisma ORM for type-safe database operations
- PostgreSQL with pgvector for embeddings

### 4. Database Schema (`apps/server/prisma/schema/`)

**Core Tables:**
- **Workspaces**: Multi-tenant organization structure
- **Users**: User management with role-based access
- **Projects**: Web3 projects being analyzed
- **Reports**: Generated analysis reports with status tracking
- **Agent Runs**: Individual agent execution tracking
- **Data Sources**: External data source management
- **Embeddings**: Vector embeddings for semantic search

## Data Flow

### Analysis Process

1. **Input Processing**
   - User submits domain/Twitter handle
   - System validates and normalizes input
   - Creates or finds existing project record

2. **Agent Orchestration**
   - Lead Research Agent analyzes input
   - Creates research plan and task decomposition
   - Launches 5-6 specialized agents in parallel

3. **Data Collection**
   - Each agent collects domain-specific data
   - Real-time progress updates via database
   - Results stored with source attribution

4. **Report Generation**
   - Lead agent synthesizes all results
   - Generates comprehensive CMA report
   - Updates database with final report

5. **Real-time Updates**
   - Frontend polls for progress updates
   - Live agent status and progress bars
   - Activity feed with detailed logging

## Key Features

### Real-time Progress Tracking
- Live agent status monitoring
- Progress bars for each research component
- Detailed activity logging with timestamps
- Error handling and recovery status

### Multi-Agent Intelligence
- Parallel execution for 90% faster analysis
- Specialized agents for different data domains
- Intelligent task decomposition and coordination
- Cross-agent data sharing and synthesis

### Comprehensive Data Sources
- On-chain metrics (TVL, transactions, token data)
- Social sentiment (Twitter, Discord, community)
- Technical assessment (smart contracts, security)
- Competitive intelligence (market positioning)
- Tokenomics analysis (distribution, utility)

### Report Generation
- Executive summary with key insights
- Market analysis and competitive landscape
- Technical and tokenomics deep dives
- Strategic recommendations for marketing/BD
- Source attribution and verification

## Development Workflow

### Getting Started
```bash
# Install dependencies
bun install

# Start development servers
bun dev                 # All apps
bun dev:web            # Frontend only (port 3001)
bun dev:server         # Backend only (port 3000)

# Database operations
bun db:push            # Push schema changes
bun db:studio          # Open database UI
bun db:generate        # Generate Prisma client
```

### Package Scripts
- `bun build`: Build all applications
- `bun check-types`: TypeScript type checking
- `bun db:migrate`: Run database migrations

## Configuration

### Environment Variables
- `DATABASE_URL`: PostgreSQL connection string
- `DIRECT_URL`: Direct database connection for migrations
- `OPENAI_API_KEY`: OpenAI API key for AI agents
- `FIRECRAWL_API_KEY`: Firecrawl API key for web scraping
- `EXA_API_KEY`: Exa API key for neural search

### Database Setup
1. Set up PostgreSQL database
2. Configure environment variables in `apps/server/.env`
3. Run `bun db:push` to create tables
4. Optionally run `bun db:studio` to view data

## Deployment

The platform is designed for cloud deployment with:
- Vercel for frontend and API hosting
- Supabase for database and real-time features
- Edge functions for low-latency AI processing
- Horizontal scaling for agent orchestration

## Future Enhancements

Based on the PRD, planned features include:
- White-label options for enterprise clients
- Advanced visualization and interactive charts
- Team collaboration and report sharing
- Custom report templates and branding
- API access for third-party integrations
- Automated monitoring and alerts
