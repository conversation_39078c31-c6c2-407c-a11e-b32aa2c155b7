# Hosted Supabase Setup Guide

This guide walks you through setting up your CMA application with a hosted Supabase instance instead of the local development setup.

## Prerequisites

- ✅ Hosted Supabase project created
- ✅ Supabase credentials (URL, Anon Key, Service Role Key)
- ✅ Backend environment updated with hosted credentials

## Step 1: Update Environment Variables

### Backend Environment (`apps/server/.env`)

Make sure your backend environment has the hosted Supabase credentials:

```env
# Remove these local database URLs
# DATABASE_URL=postgresql://postgres:postgres@127.0.0.1:54322/postgres
# DIRECT_URL=postgresql://postgres:postgres@127.0.0.1:54322/postgres

# Hosted Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project-ref.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
```

### Frontend Environment (`apps/web/.env`)

Update your frontend environment to point to the hosted instance:

```env
# Remove local Supabase configuration
# NEXT_PUBLIC_SUPABASE_URL=http://127.0.0.1:54321
# NEXT_PUBLIC_SUPABASE_ANON_KEY=local-anon-key

# Hosted Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project-ref.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
```

## Step 2: Create Database Schema

### 2.1 Initial Schema (Tables, Indexes, Functions)

Go to your Supabase dashboard → SQL Editor and execute the following SQL:

```sql
-- Enable the pgvector extension for vector embeddings
CREATE EXTENSION IF NOT EXISTS vector;

-- Create workspaces table
CREATE TABLE workspaces (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  slug TEXT UNIQUE NOT NULL,
  plan TEXT CHECK (plan IN ('free', 'pro', 'enterprise')) DEFAULT 'free',
  settings JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create users table
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email TEXT UNIQUE NOT NULL,
  full_name TEXT,
  avatar_url TEXT,
  workspace_id UUID REFERENCES workspaces(id) ON DELETE SET NULL,
  role TEXT CHECK (role IN ('admin', 'member', 'viewer')) DEFAULT 'member',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create projects table
CREATE TABLE projects (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  type TEXT CHECK (type IN ('domain', 'twitter', 'contract')) NOT NULL,
  identifier TEXT NOT NULL,
  description TEXT,
  website TEXT,
  twitter_handle TEXT,
  github_url TEXT,
  contract_addresses JSONB DEFAULT '[]',
  metadata JSONB DEFAULT '{}',
  created_by UUID REFERENCES users(id) ON DELETE CASCADE,
  workspace_id UUID REFERENCES workspaces(id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create reports table
CREATE TABLE reports (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  status TEXT CHECK (status IN ('pending', 'generating', 'completed', 'failed')) DEFAULT 'pending',
  depth TEXT CHECK (depth IN ('quick', 'standard', 'deep')) DEFAULT 'standard',
  content JSONB DEFAULT '{}',
  executive_summary TEXT,
  market_analysis JSONB DEFAULT '{}',
  competitive_landscape JSONB DEFAULT '{}',
  technical_analysis JSONB DEFAULT '{}',
  tokenomics_analysis JSONB DEFAULT '{}',
  growth_metrics JSONB DEFAULT '{}',
  recommendations JSONB DEFAULT '{}',
  sources JSONB DEFAULT '[]',
  generated_by UUID REFERENCES users(id) ON DELETE CASCADE,
  workspace_id UUID REFERENCES workspaces(id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  completed_at TIMESTAMPTZ
);

-- Create agent_runs table
CREATE TABLE agent_runs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  report_id UUID REFERENCES reports(id) ON DELETE CASCADE,
  agent_type TEXT NOT NULL,
  status TEXT CHECK (status IN ('pending', 'running', 'completed', 'failed')) DEFAULT 'pending',
  input_data JSONB DEFAULT '{}',
  output_data JSONB DEFAULT '{}',
  error_message TEXT,
  metrics JSONB DEFAULT '{}',
  started_at TIMESTAMPTZ,
  completed_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create data_sources table for caching external API responses
CREATE TABLE data_sources (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  source_type TEXT NOT NULL,
  source_url TEXT NOT NULL,
  data JSONB NOT NULL,
  hash TEXT NOT NULL,
  confidence_score FLOAT CHECK (confidence_score >= 0 AND confidence_score <= 1),
  last_updated TIMESTAMPTZ DEFAULT NOW(),
  expires_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create embeddings table for vector search
CREATE TABLE embeddings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  content TEXT NOT NULL,
  embedding vector(1536), -- OpenAI embeddings are 1536 dimensions
  metadata JSONB DEFAULT '{}',
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  source_id UUID REFERENCES data_sources(id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_projects_workspace_id ON projects(workspace_id);
CREATE INDEX idx_projects_type_identifier ON projects(type, identifier);
CREATE INDEX idx_reports_project_id ON reports(project_id);
CREATE INDEX idx_reports_status ON reports(status);
CREATE INDEX idx_agent_runs_report_id ON agent_runs(report_id);
CREATE INDEX idx_agent_runs_status ON agent_runs(status);
CREATE INDEX idx_data_sources_project_id ON data_sources(project_id);
CREATE INDEX idx_data_sources_hash ON data_sources(hash);
CREATE INDEX idx_data_sources_expires_at ON data_sources(expires_at);
CREATE INDEX idx_embeddings_project_id ON embeddings(project_id);

-- Create vector similarity search index
CREATE INDEX idx_embeddings_vector ON embeddings USING ivfflat (embedding vector_cosine_ops)
WITH (lists = 100);

-- Create function for vector similarity search
CREATE OR REPLACE FUNCTION match_embeddings(
  query_embedding vector(1536),
  similarity_threshold float DEFAULT 0.7,
  match_count int DEFAULT 10
)
RETURNS TABLE (
  id uuid,
  content text,
  similarity float,
  metadata jsonb
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT
    embeddings.id,
    embeddings.content,
    1 - (embeddings.embedding <=> query_embedding) AS similarity,
    embeddings.metadata
  FROM embeddings
  WHERE 1 - (embeddings.embedding <=> query_embedding) > similarity_threshold
  ORDER BY embeddings.embedding <=> query_embedding
  LIMIT match_count;
END;
$$;

-- Enable RLS (Row Level Security)
ALTER TABLE workspaces ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE agent_runs ENABLE ROW LEVEL SECURITY;
ALTER TABLE data_sources ENABLE ROW LEVEL SECURITY;
ALTER TABLE embeddings ENABLE ROW LEVEL SECURITY;

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Add updated_at triggers
CREATE TRIGGER update_workspaces_updated_at BEFORE UPDATE ON workspaces FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_projects_updated_at BEFORE UPDATE ON projects FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_reports_updated_at BEFORE UPDATE ON reports FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_agent_runs_updated_at BEFORE UPDATE ON agent_runs FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

### 2.2 Row Level Security Policies

After the initial schema is created, execute these RLS policies:

```sql
-- RLS Policies for workspaces
CREATE POLICY "Users can view their own workspace"
  ON workspaces FOR SELECT
  USING (id IN (
    SELECT workspace_id FROM users WHERE id = auth.uid()
  ));

-- RLS Policies for users
CREATE POLICY "Users can view users in their workspace"
  ON users FOR SELECT
  USING (workspace_id IN (
    SELECT workspace_id FROM users WHERE id = auth.uid()
  ));

CREATE POLICY "Users can update their own profile"
  ON users FOR UPDATE
  USING (id = auth.uid());

-- RLS Policies for projects
CREATE POLICY "Users can view projects in their workspace"
  ON projects FOR SELECT
  USING (workspace_id IN (
    SELECT workspace_id FROM users WHERE id = auth.uid()
  ));

CREATE POLICY "Users can create projects in their workspace"
  ON projects FOR INSERT
  WITH CHECK (workspace_id IN (
    SELECT workspace_id FROM users WHERE id = auth.uid()
  ));

CREATE POLICY "Users can update projects in their workspace"
  ON projects FOR UPDATE
  USING (workspace_id IN (
    SELECT workspace_id FROM users WHERE id = auth.uid()
  ));

-- RLS Policies for reports
CREATE POLICY "Users can view reports in their workspace"
  ON reports FOR SELECT
  USING (workspace_id IN (
    SELECT workspace_id FROM users WHERE id = auth.uid()
  ));

CREATE POLICY "Users can create reports in their workspace"
  ON reports FOR INSERT
  WITH CHECK (workspace_id IN (
    SELECT workspace_id FROM users WHERE id = auth.uid()
  ));

CREATE POLICY "Users can update reports in their workspace"
  ON reports FOR UPDATE
  USING (workspace_id IN (
    SELECT workspace_id FROM users WHERE id = auth.uid()
  ));

-- RLS Policies for agent_runs
CREATE POLICY "Users can view agent runs for their workspace reports"
  ON agent_runs FOR SELECT
  USING (report_id IN (
    SELECT id FROM reports WHERE workspace_id IN (
      SELECT workspace_id FROM users WHERE id = auth.uid()
    )
  ));

CREATE POLICY "System can manage agent runs"
  ON agent_runs FOR ALL
  USING (true);

-- RLS Policies for data_sources
CREATE POLICY "Users can view data sources for their workspace projects"
  ON data_sources FOR SELECT
  USING (project_id IN (
    SELECT id FROM projects WHERE workspace_id IN (
      SELECT workspace_id FROM users WHERE id = auth.uid()
    )
  ));

CREATE POLICY "System can manage data sources"
  ON data_sources FOR ALL
  USING (true);

-- RLS Policies for embeddings
CREATE POLICY "Users can view embeddings for their workspace projects"
  ON embeddings FOR SELECT
  USING (project_id IN (
    SELECT id FROM projects WHERE workspace_id IN (
      SELECT workspace_id FROM users WHERE id = auth.uid()
    )
  ));

CREATE POLICY "System can manage embeddings"
  ON embeddings FOR ALL
  USING (true);
```

## Step 3: Create Default Data

After the schema is set up, create the default workspace and system user:

```sql
-- Insert default workspace
INSERT INTO workspaces (name, slug, plan) 
VALUES ('Default Workspace', 'default', 'free')
ON CONFLICT (slug) DO NOTHING;

-- Insert system user (get workspace ID first)
INSERT INTO users (email, full_name, workspace_id, role)
SELECT 
  '<EMAIL>',
  'System User', 
  id,
  'admin'
FROM workspaces 
WHERE slug = 'default'
ON CONFLICT (email) DO NOTHING;
```

## Step 4: Test Connection

Run your application and test that:

1. ✅ tRPC endpoints connect successfully
2. ✅ Database queries work
3. ✅ SSE connections function properly
4. ✅ Analysis workflow completes

## Step 5: Verification Commands

You can verify your setup by running these test commands:

```bash
# Test tRPC health check
curl -s "http://localhost:3000/trpc/healthCheck?batch=1&input=%7B%7D"

# Test list reports
curl -s "http://localhost:3000/trpc/listReports?batch=1&input=%7B%220%22%3A%7B%22limit%22%3A5%7D%7D"

# Test analysis (replace with your data)
curl -s -X POST "http://localhost:3000/trpc/startAnalysis?batch=1" \
  -H "Content-Type: application/json" \
  -d '{"0": {"type": "domain", "value": "test.com", "reportDepth": "standard"}}'
```

## Troubleshooting

### Common Issues

1. **Connection Errors**: Verify your Supabase URL includes `https://`
2. **Permission Errors**: Ensure you're using the service role key for backend operations
3. **Missing Tables**: Double-check all SQL was executed successfully
4. **RLS Blocking Queries**: Ensure default data (workspace/user) was created

### Environment Checklist

- [ ] Backend `.env` has hosted Supabase credentials
- [ ] Frontend `.env` has hosted Supabase credentials  
- [ ] All SQL schemas executed successfully
- [ ] Default workspace and user created
- [ ] Application connects and queries work

## Production Considerations

When moving to production:

1. **Security**: Review and tighten RLS policies as needed
2. **Performance**: Monitor query performance and add indexes if needed
3. **Backup**: Set up automated backups in Supabase dashboard
4. **Monitoring**: Enable logging and monitoring for database queries
5. **Authentication**: Implement proper user authentication (currently using system user)

---

**🎉 Congratulations!** Your CMA application is now running on hosted Supabase with a complete schema and proper security policies.