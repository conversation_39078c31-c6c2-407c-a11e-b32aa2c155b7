{"name": "server", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "db:push": "prisma db push --schema ./prisma/schema", "db:studio": "prisma studio", "db:generate": "prisma generate --schema ./prisma/schema", "db:migrate": "prisma migrate dev"}, "dependencies": {"@ai-sdk/openai": "^1.3.22", "@mendable/firecrawl-js": "^1.29.1", "@prisma/client": "^6.9.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.50.3", "@trpc/client": "^11.4.2", "@trpc/server": "^11.4.2", "ai": "^4.3.16", "dotenv": "^16.5.0", "exa-js": "^1.8.19", "next": "15.3.0", "openai": "^5.8.2", "viem": "^2.31.6"}, "trustedDependencies": ["supabase"], "devDependencies": {"@types/node": "^20", "@types/react": "^19", "typescript": "^5", "prisma": "^6.9.0"}}