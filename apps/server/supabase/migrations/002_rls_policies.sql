-- RLS Policies for workspaces
CREATE POLICY "Users can view their own workspace"
  ON workspaces FOR SELECT
  USING (id IN (
    SELECT workspace_id FROM users WHERE id = auth.uid()
  ));

-- RLS Policies for users
CREATE POLICY "Users can view users in their workspace"
  ON users FOR SELECT
  USING (workspace_id IN (
    SELECT workspace_id FROM users WHERE id = auth.uid()
  ));

CREATE POLICY "Users can update their own profile"
  ON users FOR UPDATE
  USING (id = auth.uid());

-- RLS Policies for projects
CREATE POLICY "Users can view projects in their workspace"
  ON projects FOR SELECT
  USING (workspace_id IN (
    SELECT workspace_id FROM users WHERE id = auth.uid()
  ));

CREATE POLICY "Users can create projects in their workspace"
  ON projects FOR INSERT
  WITH CHECK (workspace_id IN (
    SELECT workspace_id FROM users WHERE id = auth.uid()
  ));

CREATE POLICY "Users can update projects in their workspace"
  ON projects FOR UPDATE
  USING (workspace_id IN (
    SELECT workspace_id FROM users WHERE id = auth.uid()
  ));

-- RLS Policies for reports
CREATE POLICY "Users can view reports in their workspace"
  ON reports FOR SELECT
  USING (workspace_id IN (
    SELECT workspace_id FROM users WHERE id = auth.uid()
  ));

CREATE POLICY "Users can create reports in their workspace"
  ON reports FOR INSERT
  WITH CHECK (workspace_id IN (
    SELECT workspace_id FROM users WHERE id = auth.uid()
  ));

CREATE POLICY "Users can update reports in their workspace"
  ON reports FOR UPDATE
  USING (workspace_id IN (
    SELECT workspace_id FROM users WHERE id = auth.uid()
  ));

-- RLS Policies for agent_runs
CREATE POLICY "Users can view agent runs for their workspace reports"
  ON agent_runs FOR SELECT
  USING (report_id IN (
    SELECT id FROM reports WHERE workspace_id IN (
      SELECT workspace_id FROM users WHERE id = auth.uid()
    )
  ));

CREATE POLICY "System can manage agent runs"
  ON agent_runs FOR ALL
  USING (true);

-- RLS Policies for data_sources
CREATE POLICY "Users can view data sources for their workspace projects"
  ON data_sources FOR SELECT
  USING (project_id IN (
    SELECT id FROM projects WHERE workspace_id IN (
      SELECT workspace_id FROM users WHERE id = auth.uid()
    )
  ));

CREATE POLICY "System can manage data sources"
  ON data_sources FOR ALL
  USING (true);

-- RLS Policies for embeddings
CREATE POLICY "Users can view embeddings for their workspace projects"
  ON embeddings FOR SELECT
  USING (project_id IN (
    SELECT id FROM projects WHERE workspace_id IN (
      SELECT workspace_id FROM users WHERE id = auth.uid()
    )
  ));

CREATE POLICY "System can manage embeddings"
  ON embeddings FOR ALL
  USING (true);