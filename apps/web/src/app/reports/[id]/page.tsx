"use client"
import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { ChevronDown, ChevronRight, Download, Share, Star, TrendingUp, TrendingDown, AlertTriangle } from "lucide-react";

interface AgentResult {
  agent_name: string;
  agent_label: string;
  status: 'completed' | 'failed' | 'running';
  score: number;
  confidence: number;
  key_findings: string[];
  detailed_analysis: {
    [section: string]: any;
  };
  charts?: {
    type: 'line' | 'bar' | 'pie' | 'scatter';
    data: any[];
    title: string;
  }[];
  recommendations: string[];
  risks: string[];
  opportunities: string[];
}

interface Report {
  id: string;
  project_name: string;
  project_domain: string;
  title: string;
  overall_score: number;
  status: 'completed' | 'generating' | 'failed';
  created_at: string;
  generation_time_ms: number;
  total_pages: number;
  format: string;
  executive_summary: string;
  agents_results: AgentResult[];
  metadata: {
    project_type: string;
    blockchain: string;
    category: string;
    market_cap?: number;
    tvl?: number;
  };
}

const MOCK_REPORT: Report = {
  id: "report_1",
  project_name: "Uniswap",
  project_domain: "uniswap.org",
  title: "Uniswap - Comprehensive Market Analysis Report",
  overall_score: 8.2,
  status: "completed",
  created_at: "2024-07-03T10:30:00Z",
  generation_time_ms: 180000,
  total_pages: 24,
  format: "markdown",
  executive_summary: "Uniswap continues to be a dominant force in the decentralized exchange landscape, with strong fundamentals, robust technical infrastructure, and significant market positioning. Our analysis reveals excellent tokenomics design and strong community sentiment, though some concerns exist around competitive pressure and regulatory uncertainties.",
  metadata: {
    project_type: "DeFi Protocol",
    blockchain: "Ethereum",
    category: "Decentralized Exchange",
    market_cap: 4200000000,
    tvl: 2100000000,
  },
  agents_results: [
    {
      agent_name: "lead_research",
      agent_label: "Lead Research",
      status: "completed",
      score: 8.5,
      confidence: 95,
      key_findings: [
        "Established market leader with 65% DEX market share",
        "Strong brand recognition and developer ecosystem",
        "Active governance and community participation",
        "Continuous innovation with V4 protocol development"
      ],
      detailed_analysis: {
        market_position: {
          market_share: 65,
          ranking: 1,
          competitors: ["Curve", "Balancer", "SushiSwap"],
          differentiation: "Automated Market Maker with concentrated liquidity"
        },
        financial_metrics: {
          daily_volume: **********,
          weekly_fees: 25000000,
          protocol_revenue: 15000000
        }
      },
      recommendations: [
        "Continue V4 development to maintain competitive edge",
        "Expand cross-chain presence",
        "Strengthen institutional partnerships"
      ],
      risks: [
        "Regulatory scrutiny on DeFi protocols",
        "MEV and front-running concerns",
        "Competition from newer AMM models"
      ],
      opportunities: [
        "Layer 2 expansion opportunities",
        "Integration with traditional finance",
        "Cross-chain bridge development"
      ]
    },
    {
      agent_name: "onchain_analytics",
      agent_label: "On-Chain Analytics",
      status: "completed",
      score: 8.8,
      confidence: 92,
      key_findings: [
        "$2.1B Total Value Locked across all versions",
        "1.2M daily active users with consistent growth",
        "Strong liquidity distribution across top pairs",
        "Efficient capital utilization with V3 concentrated liquidity"
      ],
      detailed_analysis: {
        tvl_breakdown: {
          v2: 300000000,
          v3: 1800000000,
          top_pairs: ["USDC/ETH", "USDT/USDC", "WETH/USDT"]
        },
        user_metrics: {
          daily_active_users: 1200000,
          monthly_active_users: 5800000,
          retention_rate: 78
        },
        volume_analysis: {
          daily_volume: **********,
          volume_growth_30d: 12.5,
          fee_generation: 3600000
        }
      },
      recommendations: [
        "Optimize gas costs for smaller transactions",
        "Enhance LP rewards distribution",
        "Improve capital efficiency metrics"
      ],
      risks: [
        "High gas costs on Ethereum mainnet",
        "Impermanent loss for liquidity providers",
        "Smart contract risks"
      ],
      opportunities: [
        "Layer 2 scaling solutions adoption",
        "Institutional liquidity provision",
        "Cross-chain expansion"
      ]
    },
    {
      agent_name: "social_sentiment",
      agent_label: "Social Sentiment",
      status: "completed",
      score: 7.8,
      confidence: 88,
      key_findings: [
        "Overall positive sentiment across social platforms",
        "Strong developer community engagement",
        "High trust score among DeFi users",
        "Active governance participation"
      ],
      detailed_analysis: {
        twitter_sentiment: {
          mentions_24h: 15600,
          positive_ratio: 72,
          negative_ratio: 15,
          neutral_ratio: 13,
          influencer_mentions: 24
        },
        reddit_analysis: {
          active_discussions: 156,
          upvote_ratio: 84,
          community_size: 245000
        },
        discord_activity: {
          active_members: 45000,
          daily_messages: 1200,
          support_response_time: "< 2 hours"
        }
      },
      recommendations: [
        "Increase educational content creation",
        "Strengthen community governance initiatives",
        "Address user concerns about gas costs"
      ],
      risks: [
        "Negative sentiment around high fees",
        "Competition narrative from other DEXs",
        "Regulatory concerns discussions"
      ],
      opportunities: [
        "Community-driven marketing campaigns",
        "Educational partnership opportunities",
        "Governance token utility expansion"
      ]
    },
    {
      agent_name: "market_positioning",
      agent_label: "Market Positioning",
      status: "completed",
      score: 8.4,
      confidence: 90,
      key_findings: [
        "Dominant market position with strong moats",
        "First-mover advantage in AMM space",
        "Strong network effects and liquidity",
        "Continuous innovation maintaining leadership"
      ],
      detailed_analysis: {
        competitive_landscape: {
          market_share: 65,
          key_competitors: ["Curve Finance", "Balancer", "SushiSwap"],
          competitive_advantages: ["Liquidity", "Brand", "Innovation"],
          threats: ["Newer AMM models", "Cross-chain competitors"]
        },
        market_trends: {
          defi_growth: 15.2,
          dex_volume_growth: 8.7,
          institutional_adoption: 23.1
        }
      },
      recommendations: [
        "Accelerate cross-chain expansion",
        "Strengthen institutional partnerships",
        "Continue technical innovation"
      ],
      risks: [
        "Increased competition from Layer 2 DEXs",
        "Regulatory pressure on DeFi",
        "Ethereum scalability limitations"
      ],
      opportunities: [
        "Layer 2 first-mover advantage",
        "Institutional DeFi adoption",
        "Cross-chain bridge aggregation"
      ]
    }
  ]
};

export default function ReportViewer() {
  const params = useParams();
  const [report, setReport] = useState<Report | null>(null);
  const [activeAgent, setActiveAgent] = useState<string>("overview");
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set());
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Simulate loading the report
    setTimeout(() => {
      setReport(MOCK_REPORT);
      setLoading(false);
    }, 1000);
  }, [params.id]);

  const toggleSection = (section: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(section)) {
      newExpanded.delete(section);
    } else {
      newExpanded.add(section);
    }
    setExpandedSections(newExpanded);
  };

  const getScoreColor = (score: number) => {
    if (score >= 8) return "text-green-600";
    if (score >= 6) return "text-yellow-600";
    return "text-red-600";
  };

  const getScoreIcon = (score: number) => {
    if (score >= 8) return <TrendingUp className="w-4 h-4 text-green-600" />;
    if (score >= 6) return <Star className="w-4 h-4 text-yellow-600" />;
    return <TrendingDown className="w-4 h-4 text-red-600" />;
  };

  const formatNumber = (num: number) => {
    if (num >= 1e9) return `$${(num / 1e9).toFixed(1)}B`;
    if (num >= 1e6) return `$${(num / 1e6).toFixed(1)}M`;
    if (num >= 1e3) return `$${(num / 1e3).toFixed(1)}K`;
    return `$${num}`;
  };

  if (loading) {
    return (
      <div className="container mx-auto max-w-7xl px-4 py-8">
        <div className="animate-pulse space-y-8">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="h-32 bg-gray-200 rounded"></div>
          <div className="grid md:grid-cols-3 gap-6">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (!report) {
    return (
      <div className="container mx-auto max-w-7xl px-4 py-8">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-8">
              <AlertTriangle className="w-12 h-12 text-yellow-500 mx-auto mb-4" />
              <h2 className="text-xl font-semibold mb-2">Report Not Found</h2>
              <p className="text-muted-foreground">The requested report could not be found.</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto max-w-7xl px-4 py-8 space-y-8">
      {/* Header */}
      <div className="space-y-4">
        <div className="flex items-start justify-between">
          <div className="space-y-2">
            <h1 className="text-3xl font-bold tracking-tight">{report.title}</h1>
            <div className="flex items-center gap-4 text-sm text-muted-foreground">
              <span>Project: {report.project_name}</span>
              <span>•</span>
              <span>Domain: {report.project_domain}</span>
              <span>•</span>
              <span>{new Date(report.created_at).toLocaleDateString()}</span>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm">
              <Share className="w-4 h-4 mr-2" />
              Share
            </Button>
            <Button size="sm">
              <Download className="w-4 h-4 mr-2" />
              Download
            </Button>
          </div>
        </div>

        {/* Overall Score & Metadata */}
        <div className="grid md:grid-cols-5 gap-6">
          <Card>
            <CardContent className="pt-6">
              <div className="text-center space-y-2">
                <div className={`text-3xl font-bold ${getScoreColor(report.overall_score)}`}>
                  {report.overall_score}/10
                </div>
                <p className="text-sm text-muted-foreground">Overall Score</p>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="text-center space-y-2">
                <div className="text-2xl font-bold text-blue-600">
                  {formatNumber(report.metadata.market_cap || 0)}
                </div>
                <p className="text-sm text-muted-foreground">Market Cap</p>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="text-center space-y-2">
                <div className="text-2xl font-bold text-green-600">
                  {formatNumber(report.metadata.tvl || 0)}
                </div>
                <p className="text-sm text-muted-foreground">Total TVL</p>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="text-center space-y-2">
                <div className="text-lg font-bold">{report.metadata.blockchain}</div>
                <p className="text-sm text-muted-foreground">Blockchain</p>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="text-center space-y-2">
                <div className="text-lg font-bold">{report.metadata.category}</div>
                <p className="text-sm text-muted-foreground">Category</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Report Content */}
      <Tabs value={activeAgent} onValueChange={setActiveAgent}>
        <TabsList className="grid w-full grid-cols-7">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          {report.agents_results.map(agent => (
            <TabsTrigger key={agent.agent_name} value={agent.agent_name}>
              {agent.agent_label}
            </TabsTrigger>
          ))}
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Executive Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground leading-relaxed">
                {report.executive_summary}
              </p>
            </CardContent>
          </Card>

          {/* Agent Scores Grid */}
          <Card>
            <CardHeader>
              <CardTitle>Analysis Breakdown</CardTitle>
              <CardDescription>
                Detailed scores from each specialized analysis agent
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                {report.agents_results.map(agent => (
                  <Card key={agent.agent_name} className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => setActiveAgent(agent.agent_name)}>
                    <CardContent className="pt-6">
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <h4 className="font-medium">{agent.agent_label}</h4>
                          {getScoreIcon(agent.score)}
                        </div>
                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span>Score</span>
                            <span className={`font-medium ${getScoreColor(agent.score)}`}>
                              {agent.score}/10
                            </span>
                          </div>
                          <Progress value={agent.score * 10} className="h-2" />
                          <div className="flex justify-between text-xs text-muted-foreground">
                            <span>Confidence</span>
                            <span>{agent.confidence}%</span>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Key Insights */}
          <div className="grid md:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-green-600">Key Strengths</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {report.agents_results.flatMap(agent => 
                  agent.opportunities.slice(0, 1)
                ).slice(0, 5).map((strength, index) => (
                  <div key={index} className="flex items-start gap-2 text-sm">
                    <div className="w-2 h-2 rounded-full bg-green-500 mt-2 flex-shrink-0"></div>
                    <span>{strength}</span>
                  </div>
                ))}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-yellow-600">Key Risks</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {report.agents_results.flatMap(agent => 
                  agent.risks.slice(0, 1)
                ).slice(0, 5).map((risk, index) => (
                  <div key={index} className="flex items-start gap-2 text-sm">
                    <div className="w-2 h-2 rounded-full bg-yellow-500 mt-2 flex-shrink-0"></div>
                    <span>{risk}</span>
                  </div>
                ))}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-blue-600">Recommendations</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {report.agents_results.flatMap(agent => 
                  agent.recommendations.slice(0, 1)
                ).slice(0, 5).map((rec, index) => (
                  <div key={index} className="flex items-start gap-2 text-sm">
                    <div className="w-2 h-2 rounded-full bg-blue-500 mt-2 flex-shrink-0"></div>
                    <span>{rec}</span>
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Individual Agent Tabs */}
        {report.agents_results.map(agent => (
          <TabsContent key={agent.agent_name} value={agent.agent_name} className="space-y-6">
            {/* Agent Header */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>{agent.agent_label} Analysis</CardTitle>
                    <CardDescription>
                      Confidence: {agent.confidence}% • Status: {agent.status}
                    </CardDescription>
                  </div>
                  <div className="text-right">
                    <div className={`text-2xl font-bold ${getScoreColor(agent.score)}`}>
                      {agent.score}/10
                    </div>
                    <p className="text-sm text-muted-foreground">Agent Score</p>
                  </div>
                </div>
              </CardHeader>
            </Card>

            {/* Key Findings */}
            <Card>
              <CardHeader>
                <button
                  className="flex items-center justify-between w-full text-left"
                  onClick={() => toggleSection(`${agent.agent_name}_findings`)}
                >
                  <CardTitle>Key Findings</CardTitle>
                  {expandedSections.has(`${agent.agent_name}_findings`) ? 
                    <ChevronDown className="w-5 h-5" /> : 
                    <ChevronRight className="w-5 h-5" />
                  }
                </button>
              </CardHeader>
              {expandedSections.has(`${agent.agent_name}_findings`) && (
                <CardContent className="space-y-3">
                  {agent.key_findings.map((finding, index) => (
                    <div key={index} className="flex items-start gap-3">
                      <div className="w-6 h-6 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center text-sm font-medium flex-shrink-0">
                        {index + 1}
                      </div>
                      <p>{finding}</p>
                    </div>
                  ))}
                </CardContent>
              )}
            </Card>

            {/* Detailed Analysis */}
            <Card>
              <CardHeader>
                <button
                  className="flex items-center justify-between w-full text-left"
                  onClick={() => toggleSection(`${agent.agent_name}_analysis`)}
                >
                  <CardTitle>Detailed Analysis</CardTitle>
                  {expandedSections.has(`${agent.agent_name}_analysis`) ? 
                    <ChevronDown className="w-5 h-5" /> : 
                    <ChevronRight className="w-5 h-5" />
                  }
                </button>
              </CardHeader>
              {expandedSections.has(`${agent.agent_name}_analysis`) && (
                <CardContent>
                  <div className="space-y-6">
                    {Object.entries(agent.detailed_analysis).map(([section, data]) => (
                      <div key={section}>
                        <h4 className="font-medium mb-3 capitalize">
                          {section.replace(/_/g, ' ')}
                        </h4>
                        <div className="grid md:grid-cols-2 gap-4">
                          {Object.entries(data as Record<string, any>).map(([key, value]) => (
                            <div key={key} className="space-y-1">
                              <p className="text-sm text-muted-foreground capitalize">
                                {key.replace(/_/g, ' ')}
                              </p>
                              <p className="font-medium">
                                {typeof value === 'number' ? 
                                  (key.includes('rate') || key.includes('ratio') || key.includes('share') ? 
                                    `${value}%` : 
                                    formatNumber(value)
                                  ) : 
                                  Array.isArray(value) ? 
                                    value.join(', ') : 
                                    String(value)
                                }
                              </p>
                            </div>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              )}
            </Card>

            {/* Recommendations, Risks, Opportunities */}
            <div className="grid md:grid-cols-3 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-blue-600">Recommendations</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  {agent.recommendations.map((rec, index) => (
                    <div key={index} className="flex items-start gap-2">
                      <div className="w-2 h-2 rounded-full bg-blue-500 mt-2 flex-shrink-0"></div>
                      <p className="text-sm">{rec}</p>
                    </div>
                  ))}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-red-600">Risks</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  {agent.risks.map((risk, index) => (
                    <div key={index} className="flex items-start gap-2">
                      <div className="w-2 h-2 rounded-full bg-red-500 mt-2 flex-shrink-0"></div>
                      <p className="text-sm">{risk}</p>
                    </div>
                  ))}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-green-600">Opportunities</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  {agent.opportunities.map((opp, index) => (
                    <div key={index} className="flex items-start gap-2">
                      <div className="w-2 h-2 rounded-full bg-green-500 mt-2 flex-shrink-0"></div>
                      <p className="text-sm">{opp}</p>
                    </div>
                  ))}
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
}