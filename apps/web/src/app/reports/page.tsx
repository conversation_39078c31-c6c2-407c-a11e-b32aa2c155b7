"use client"
import { useState } from "react";
import Link from "next/link";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";

interface Report {
  id: string;
  project_name: string;
  title: string;
  format: string;
  status: 'generating' | 'completed' | 'failed';
  created_at: string;
  metadata: {
    overall_score?: number;
    total_pages?: number;
    generation_time_ms?: number;
  };
}

const MOCK_REPORTS: Report[] = [
  {
    id: "report_1",
    project_name: "Uniswap",
    title: "Uniswap - Comprehensive Market Analysis Report",
    format: "markdown",
    status: "completed",
    created_at: "2024-07-03T10:30:00Z",
    metadata: {
      overall_score: 8.2,
      total_pages: 24,
      generation_time_ms: 180000,
    },
  },
  {
    id: "report_2", 
    project_name: "Aave",
    title: "Aave - Technical Assessment and Market Analysis",
    format: "pdf",
    status: "completed",
    created_at: "2024-07-03T09:15:00Z",
    metadata: {
      overall_score: 7.8,
      total_pages: 18,
      generation_time_ms: 165000,
    },
  },
  {
    id: "report_3",
    project_name: "1inch",
    title: "1inch - Competitive Market Analysis Report",
    format: "html",
    status: "generating",
    created_at: "2024-07-03T11:00:00Z",
    metadata: {},
  },
];

export default function Reports() {
  const [reports] = useState<Report[]>(MOCK_REPORTS);
  const [searchTerm, setSearchTerm] = useState("");

  const filteredReports = reports.filter(report =>
    report.project_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    report.title.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getStatusBadge = (status: string) => {
    const variants = {
      completed: "bg-green-100 text-green-800",
      generating: "bg-blue-100 text-blue-800", 
      failed: "bg-red-100 text-red-800",
    };
    
    return (
      <Badge className={variants[status as keyof typeof variants]}>
        {status}
      </Badge>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatDuration = (ms: number) => {
    const minutes = Math.floor(ms / 60000);
    const seconds = Math.floor((ms % 60000) / 1000);
    return `${minutes}m ${seconds}s`;
  };

  return (
    <div className="container mx-auto max-w-6xl px-4 py-8 space-y-8">
      {/* Header */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Reports</h1>
            <p className="text-muted-foreground">
              View and manage your generated analysis reports
            </p>
          </div>
          <Button>
            Generate New Report
          </Button>
        </div>

        {/* Search */}
        <div className="max-w-md">
          <Input
            placeholder="Search reports..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      {/* Statistics */}
      <div className="grid md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center space-y-2">
              <div className="text-2xl font-bold">{reports.length}</div>
              <p className="text-sm text-muted-foreground">Total Reports</p>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="text-center space-y-2">
              <div className="text-2xl font-bold text-green-600">
                {reports.filter(r => r.status === 'completed').length}
              </div>
              <p className="text-sm text-muted-foreground">Completed</p>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="text-center space-y-2">
              <div className="text-2xl font-bold text-blue-600">
                {reports.filter(r => r.status === 'generating').length}
              </div>
              <p className="text-sm text-muted-foreground">In Progress</p>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="text-center space-y-2">
              <div className="text-2xl font-bold">
                {Math.round(
                  reports
                    .filter(r => r.metadata.overall_score)
                    .reduce((sum, r) => sum + (r.metadata.overall_score || 0), 0) /
                  reports.filter(r => r.metadata.overall_score).length * 10
                ) / 10 || 0}
              </div>
              <p className="text-sm text-muted-foreground">Avg Score</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Reports List */}
      <div className="space-y-4">
        {filteredReports.length === 0 ? (
          <Card>
            <CardContent className="pt-6">
              <div className="text-center py-8 space-y-3">
                <p className="text-muted-foreground">
                  {searchTerm ? "No reports found matching your search." : "No reports generated yet."}
                </p>
                <Button variant="outline">
                  {searchTerm ? "Clear Search" : "Generate Your First Report"}
                </Button>
              </div>
            </CardContent>
          </Card>
        ) : (
          filteredReports.map(report => (
            <Card key={report.id} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="space-y-1">
                    <CardTitle className="text-lg">{report.title}</CardTitle>
                    <CardDescription className="flex items-center gap-2">
                      <span>Project: {report.project_name}</span>
                      <span>•</span>
                      <span>Format: {report.format.toUpperCase()}</span>
                      <span>•</span>
                      <span>{formatDate(report.created_at)}</span>
                    </CardDescription>
                  </div>
                  {getStatusBadge(report.status)}
                </div>
              </CardHeader>
              
              <CardContent className="space-y-4">
                {/* Metadata */}
                <div className="grid md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <p className="text-muted-foreground">Overall Score</p>
                    <p className="font-medium">
                      {report.metadata.overall_score 
                        ? `${report.metadata.overall_score}/10`
                        : "—"
                      }
                    </p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Pages</p>
                    <p className="font-medium">
                      {report.metadata.total_pages || "—"}
                    </p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Generation Time</p>
                    <p className="font-medium">
                      {report.metadata.generation_time_ms 
                        ? formatDuration(report.metadata.generation_time_ms)
                        : "—"
                      }
                    </p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Report ID</p>
                    <p className="font-medium font-mono text-xs">
                      {report.id}
                    </p>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex gap-2">
                  {report.status === 'completed' ? (
                    <>
                      <Button size="sm" asChild>
                        <Link href={`/reports/${report.id}`}>
                          View Report
                        </Link>
                      </Button>
                      <Button variant="outline" size="sm">
                        Download
                      </Button>
                      <Button variant="outline" size="sm">
                        Share
                      </Button>
                    </>
                  ) : report.status === 'generating' ? (
                    <>
                      <Button size="sm" disabled>
                        Generating...
                      </Button>
                      <Button variant="outline" size="sm">
                        View Progress
                      </Button>
                    </>
                  ) : (
                    <>
                      <Button size="sm" variant="destructive">
                        Retry Generation
                      </Button>
                      <Button variant="outline" size="sm">
                        View Error
                      </Button>
                    </>
                  )}
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Templates Section */}
      <Card>
        <CardHeader>
          <CardTitle>Report Templates</CardTitle>
          <CardDescription>
            Choose from pre-built templates or create custom ones
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-3 gap-4">
            <Card>
              <CardContent className="pt-6">
                <div className="space-y-3">
                  <h4 className="font-medium">Executive Summary</h4>
                  <p className="text-sm text-muted-foreground">
                    High-level overview for decision makers and investors
                  </p>
                  <Button variant="outline" size="sm" className="w-full">
                    Use Template
                  </Button>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="pt-6">
                <div className="space-y-3">
                  <h4 className="font-medium">Detailed Analysis</h4>
                  <p className="text-sm text-muted-foreground">
                    Comprehensive analysis across all dimensions
                  </p>
                  <Button variant="outline" size="sm" className="w-full">
                    Use Template
                  </Button>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="pt-6">
                <div className="space-y-3">
                  <h4 className="font-medium">Technical Report</h4>
                  <p className="text-sm text-muted-foreground">
                    Focus on technical architecture and code quality
                  </p>
                  <Button variant="outline" size="sm" className="w-full">
                    Use Template
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}