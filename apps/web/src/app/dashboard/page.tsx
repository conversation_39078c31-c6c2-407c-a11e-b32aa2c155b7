"use client"
import { useState, useEffect } from "react";
import Link from "next/link";
import { useQuery } from "@tanstack/react-query";
import { trpc } from "@/utils/trpc";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import Loader from "@/components/loader";
import { formatDistanceToNow } from "date-fns";

interface AnalysisProgress {
  agent_name: string;
  progress: number;
  status: 'pending' | 'running' | 'completed' | 'failed';
  step: string;
  message?: string;
}

// Mock data removed - agents will be fetched from API
console.log('🚀 Dashboard: Mock agents data removed, will fetch from API');

// Agent configuration that would come from API
const DEFAULT_AGENTS = [
  { name: 'lead_research', label: 'Lead Research', color: 'bg-blue-500' },
  { name: 'social_sentiment', label: 'Social Sentiment (X.AI)', color: 'bg-purple-500' },
  { name: 'competitor_analysis', label: 'Competitor Analysis', color: 'bg-green-500' },
  { name: 'market_positioning', label: 'Market Positioning', color: 'bg-indigo-500' },
];

export default function Dashboard() {
  const [progress, setProgress] = useState<Record<string, AnalysisProgress>>({});
  const [overallProgress, setOverallProgress] = useState(0);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [currentProject] = useState("uniswap.org");

  // Add robust logging for dashboard initialization
  useEffect(() => {
    console.log('🎯 Dashboard: Component mounted');
    console.log('📊 Dashboard: Available agents:', DEFAULT_AGENTS.map(a => a.name));
    console.log('🔍 Dashboard: Current project:', currentProject);
  }, [currentProject]);

  // Query for recent reports
  const reportsQuery = useQuery({
    queryKey: ['reports'],
    queryFn: () => {
      console.log('🔄 Dashboard: Fetching reports via tRPC...');
      return trpc.listReports.query({ limit: 5 });
    },
    refetchInterval: 30000, // Refetch every 30 seconds
  });

  // Add logging for query state changes
  useEffect(() => {
    if (reportsQuery.isSuccess) {
      console.log('✅ Dashboard: Reports fetched successfully:', reportsQuery.data?.reports?.length || 0, 'reports');
    }
    if (reportsQuery.isError) {
      console.error('❌ Dashboard: Failed to fetch reports:', reportsQuery.error);
    }
    if (reportsQuery.isLoading) {
      console.log('⏳ Dashboard: Loading reports...');
    }
  }, [reportsQuery.isSuccess, reportsQuery.isError, reportsQuery.isLoading, reportsQuery.data, reportsQuery.error]);

  // Simulate real-time progress updates
  useEffect(() => {
    if (!isAnalyzing) {
      console.log('⏸️ Dashboard: Analysis not running, skipping progress updates');
      return;
    }

    console.log('🚀 Dashboard: Starting analysis progress simulation for agents:', DEFAULT_AGENTS.map(a => a.name));
    const interval = setInterval(() => {
      setProgress(prev => {
        const newProgress = { ...prev };

        DEFAULT_AGENTS.forEach(agent => {
          if (!newProgress[agent.name]) {
            console.log(`🔄 Dashboard: Initializing agent ${agent.name}`);
            newProgress[agent.name] = {
              agent_name: agent.name,
              progress: 0,
              status: 'pending',
              step: 'initializing',
            };
          }

          const current = newProgress[agent.name];
          if (current.status === 'completed') return;

          if (current.status === 'pending') {
            console.log(`▶️ Dashboard: Starting agent ${agent.name} analysis`);
            newProgress[agent.name] = {
              ...current,
              status: 'running',
              step: 'collecting_data',
              message: `Starting ${agent.label.toLowerCase()} analysis...`,
            };
          } else if (current.status === 'running') {
            const increment = Math.random() * 15;
            const newProgressValue = Math.min(current.progress + increment, 100);

            if (newProgressValue >= 100) {
              console.log(`✅ Dashboard: Agent ${agent.name} completed analysis`);
              newProgress[agent.name] = {
                ...current,
                progress: 100,
                status: 'completed',
                step: 'completed',
                message: `${agent.label} analysis completed successfully`,
              };
            } else {
              const steps = ['collecting_data', 'processing', 'analyzing', 'finalizing'];
              const stepIndex = Math.floor(newProgressValue / 25);
              const newStep = steps[Math.min(stepIndex, steps.length - 1)];

              // Log step changes
              if (newStep !== current.step) {
                console.log(`🔄 Dashboard: Agent ${agent.name} moved to step: ${newStep} (${newProgressValue.toFixed(1)}%)`);
              }

              newProgress[agent.name] = {
                ...current,
                progress: newProgressValue,
                step: newStep,
                message: `Processing ${agent.label.toLowerCase()}...`,
              };
            }
          }
        });

        return newProgress;
      });
    }, 1000);

    return () => clearInterval(interval);
  }, [isAnalyzing]);

  // Calculate overall progress
  useEffect(() => {
    const agents = Object.values(progress);
    if (agents.length === 0) {
      setOverallProgress(0);
      return;
    }

    const totalProgress = agents.reduce((sum, agent) => sum + agent.progress, 0);
    const avgProgress = totalProgress / agents.length;
    const roundedProgress = Math.round(avgProgress);
    setOverallProgress(roundedProgress);

    // Log progress updates
    if (roundedProgress % 10 === 0 && roundedProgress > 0) {
      console.log(`📊 Dashboard: Overall progress: ${roundedProgress}%`);
    }

    // Check if all agents are completed
    const allCompleted = agents.every(agent => agent.status === 'completed');
    if (allCompleted && isAnalyzing) {
      console.log('🎉 Dashboard: All agents completed analysis!');
      setIsAnalyzing(false);
    }
  }, [progress, isAnalyzing]);

  const startAnalysis = () => {
    console.log('🚀 Dashboard: Starting new analysis...');
    console.log('🔄 Dashboard: Resetting progress for all agents');
    setIsAnalyzing(true);
    setProgress({});
    setOverallProgress(0);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <div className="w-2 h-2 rounded-full bg-gray-400" />;
      case 'running':
        return <Loader className="w-4 h-4" />;
      case 'completed':
        return <div className="w-2 h-2 rounded-full bg-green-500" />;
      case 'failed':
        return <div className="w-2 h-2 rounded-full bg-red-500" />;
      default:
        return <div className="w-2 h-2 rounded-full bg-gray-400" />;
    }
  };

  const completedAgents = Object.values(progress).filter(p => p.status === 'completed').length;
  const totalAgents = DEFAULT_AGENTS.length;

  const getStatusVariant = (status: string): "default" | "secondary" | "destructive" | "outline" => {
    switch (status) {
      case 'completed': return 'default';
      case 'generating': return 'secondary';
      case 'failed': return 'destructive';
      default: return 'outline';
    }
  };

  return (
    <div className="container mx-auto max-w-6xl px-4 py-8 space-y-8">
      {/* Header */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Analysis Dashboard</h1>
            <p className="text-muted-foreground">
              Project: <span className="font-medium">{currentProject}</span>
            </p>
          </div>
          <Button 
            onClick={startAnalysis} 
            disabled={isAnalyzing}
            size="lg"
          >
            {isAnalyzing ? (
              <>
                <Loader className="mr-2 h-4 w-4" />
                Analyzing...
              </>
            ) : (
              "Start New Analysis"
            )}
          </Button>
        </div>
      </div>

      {/* Overall Progress */}
      <Card>
        <CardHeader>
          <CardTitle>Overall Progress</CardTitle>
          <CardDescription>
            {completedAgents} of {totalAgents} agents completed
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between text-sm">
              <span>Progress</span>
              <span>{overallProgress}%</span>
            </div>
            <Progress value={overallProgress} className="h-3" />
            {isAnalyzing && (
              <p className="text-sm text-muted-foreground">
                Analysis in progress... This typically takes 3-5 minutes.
              </p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Agent Progress Grid */}
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
        {DEFAULT_AGENTS.map(agent => {
          const agentProgress = progress[agent.name];
          const isActive = agentProgress?.status === 'running';
          
          return (
            <Card key={agent.name} className={isActive ? 'ring-2 ring-blue-500' : ''}>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">{agent.label}</CardTitle>
                  {agentProgress && getStatusIcon(agentProgress.status)}
                </div>
              </CardHeader>
              <CardContent className="space-y-3">
                {agentProgress ? (
                  <>
                    <div className="flex items-center justify-between text-sm">
                      <span>Progress</span>
                      <span>{Math.round(agentProgress.progress)}%</span>
                    </div>
                    <Progress value={agentProgress.progress} className="h-2" />
                    <div className="space-y-1">
                      <p className="text-sm font-medium capitalize">
                        {agentProgress.step.replace(/_/g, ' ')}
                      </p>
                      {agentProgress.message && (
                        <p className="text-xs text-muted-foreground">
                          {agentProgress.message}
                        </p>
                      )}
                    </div>
                  </>
                ) : (
                  <div className="space-y-3">
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-2 w-full" />
                    <Skeleton className="h-3 w-3/4" />
                  </div>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Results Section */}
      {overallProgress === 100 && !isAnalyzing && (
        <Card>
          <CardHeader>
            <CardTitle>Analysis Complete</CardTitle>
            <CardDescription>
              Your comprehensive market analysis is ready
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid md:grid-cols-3 gap-4">
              <Card>
                <CardContent className="pt-6">
                  <div className="text-center space-y-2">
                    <div className="text-2xl font-bold text-green-600">8.2/10</div>
                    <p className="text-sm text-muted-foreground">Overall Score</p>
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="pt-6">
                  <div className="text-center space-y-2">
                    <div className="text-2xl font-bold text-blue-600">$2.1B</div>
                    <p className="text-sm text-muted-foreground">Total TVL</p>
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="pt-6">
                  <div className="text-center space-y-2">
                    <div className="text-2xl font-bold text-purple-600">Positive</div>
                    <p className="text-sm text-muted-foreground">Sentiment</p>
                  </div>
                </CardContent>
              </Card>
            </div>
            
            <div className="flex gap-4">
              <Button className="flex-1" asChild>
                <Link href="/reports/report_1">
                  View Full Report
                </Link>
              </Button>
              <Button variant="outline" className="flex-1">
                Download PDF
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Recent Reports */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Recent Reports</CardTitle>
            <Button variant="outline" size="sm" asChild>
              <Link href="/reports">View All</Link>
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {reportsQuery.isLoading ? (
            <div className="space-y-3">
              {[1, 2, 3].map(i => (
                <div key={i} className="flex items-center gap-3">
                  <Skeleton className="h-12 w-12 rounded" />
                  <div className="flex-1 space-y-2">
                    <Skeleton className="h-4 w-3/4" />
                    <Skeleton className="h-3 w-1/2" />
                  </div>
                  <Skeleton className="h-6 w-16" />
                </div>
              ))}
            </div>
          ) : reportsQuery.data?.reports && reportsQuery.data.reports.length > 0 ? (
            <div className="space-y-3">
              {reportsQuery.data.reports.map((report: any) => (
                <div key={report.id} className="flex items-center gap-3 p-3 border rounded-lg hover:bg-muted/50 transition-colors">
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <h4 className="font-medium">{report.projects?.name || 'Unknown Project'}</h4>
                      <Badge variant={getStatusVariant(report.status)}>
                        {report.status}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {report.projects?.type} • {formatDistanceToNow(new Date(report.created_at), { addSuffix: true })}
                    </p>
                  </div>
                  <Button variant="ghost" size="sm" asChild>
                    <Link href={
                      report.status === 'completed'
                        ? `/reports/${report.id}`
                        : `/analysis/${report.id}`
                    }>
                      {report.status === 'completed' ? 'View' : 'Track'}
                    </Link>
                  </Button>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <p className="text-muted-foreground mb-4">No reports yet</p>
              <Button asChild>
                <Link href="/">Start Your First Analysis</Link>
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Activity</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {Object.values(progress)
              .filter(p => p.message)
              .slice(-5)
              .reverse()
              .map((activity, index) => (
                <div key={index} className="flex items-center gap-3 text-sm">
                  <div className="text-xs text-muted-foreground">
                    {new Date().toLocaleTimeString()}
                  </div>
                  <div className="flex-1">{activity.message}</div>
                  <div className={`px-2 py-1 rounded-full text-xs ${
                    activity.status === 'completed' ? 'bg-green-100 text-green-800' :
                    activity.status === 'running' ? 'bg-blue-100 text-blue-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {activity.status}
                  </div>
                </div>
              ))
            }
            {Object.keys(progress).length === 0 && (
              <p className="text-sm text-muted-foreground text-center py-4">
                No recent activity. Start an analysis to see live updates here.
              </p>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}