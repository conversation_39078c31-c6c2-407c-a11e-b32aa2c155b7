import { useEffect, useState, useRef } from 'react';

interface ProgressData {
  type: 'connected' | 'progress' | 'finished' | 'error';
  reportId?: string;
  report?: {
    id: string;
    status: string;
    title: string;
    created_at: string;
    updated_at: string;
    completed_at?: string;
  };
  agentRuns?: Array<{
    id: string;
    agent_type: string;
    status: string;
    metrics?: any;
    started_at?: string;
    completed_at?: string;
  }>;
  overallProgress?: number;
  timestamp?: string;
  message?: string;
  status?: string;
}

interface UseRealtimeProgressReturn {
  data: ProgressData | null;
  isConnected: boolean;
  error: string | null;
  reconnect: () => void;
}

export function useRealtimeProgress(reportId: string): UseRealtimeProgressReturn {
  const [data, setData] = useState<ProgressData | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const eventSourceRef = useRef<EventSource | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isUnmountingRef = useRef(false);

  const connect = () => {
    // Validate reportId before attempting connection
    if (!reportId || reportId === 'undefined' || reportId.trim() === '') {
      console.warn('⚠️ Invalid reportId provided to SSE hook:', reportId);
      setError('Invalid report ID');
      return;
    }

    // Close existing connection and clear any pending reconnections
    if (eventSourceRef.current) {
      console.log('🔌 Closing existing SSE connection');
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }
    
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    try {
      console.log(`🔌 Connecting to SSE for report ${reportId}`);
      
      const serverUrl = process.env.NEXT_PUBLIC_SERVER_URL || 'http://localhost:3000';
      const sseUrl = `${serverUrl}/api/progress/${reportId}`;
      
      console.log(`🔗 SSE URL: ${sseUrl}`);
      
      const eventSource = new EventSource(sseUrl);
      
      eventSourceRef.current = eventSource;

      eventSource.onopen = () => {
        console.log('✅ SSE connection opened');
        setIsConnected(true);
        setError(null);
      };

      eventSource.onmessage = (event) => {
        try {
          // Log raw data for debugging
          console.log('📨 Raw SSE data:', event.data);
          
          // Check if data is empty or invalid
          if (!event.data || event.data.trim() === '') {
            console.warn('⚠️ Received empty SSE data');
            return;
          }
          
          const progressData: ProgressData = JSON.parse(event.data);
          console.log('📊 Parsed progress update:', progressData);
          
          setData(progressData);

          // Handle different message types
          switch (progressData.type) {
            case 'connected':
              console.log('🔗 Connected to progress stream');
              break;
            case 'progress':
              // Update progress data
              break;
            case 'finished':
              console.log('🏁 Analysis finished:', progressData.status);
              // Close connection immediately to prevent server-side close error
              eventSource.close();
              setIsConnected(false);
              break;
            case 'error':
              console.error('❌ Progress error:', progressData.message);
              setError(progressData.message || 'Unknown error');
              break;
          }
        } catch (err) {
          console.error('❌ Failed to parse SSE data:', {
            error: err,
            rawData: event.data,
            dataType: typeof event.data,
            dataLength: event.data?.length || 0
          });
          setError(`Failed to parse progress data: ${err instanceof Error ? err.message : 'Unknown error'}`);
        }
      };

      eventSource.onerror = (event) => {
        console.error('❌ SSE connection error:', {
          readyState: eventSource.readyState,
          url: sseUrl,
          event: event
        });
        setIsConnected(false);
        
        // Don't reconnect if we're unmounting or if analysis is complete
        if (isUnmountingRef.current || data?.type === 'finished') {
          console.log('📝 Not reconnecting - component unmounting or analysis finished');
          return;
        }
        
        // Only attempt reconnection for actual connection failures
        if (eventSource.readyState === EventSource.CONNECTING || eventSource.readyState === EventSource.OPEN) {
          setError('Connection lost, attempting to reconnect...');
          console.log('🔄 Attempting to reconnect in 3 seconds...');
          reconnectTimeoutRef.current = setTimeout(() => {
            if (!isUnmountingRef.current) {
              connect();
            }
          }, 3000);
        } else if (eventSource.readyState === EventSource.CLOSED) {
          // Connection was properly closed by server (e.g., analysis finished)
          console.log('📝 SSE connection closed by server');
          setError(null);
        }
      };

    } catch (err) {
      console.error('Failed to create SSE connection:', err);
      setError('Failed to establish connection');
    }
  };

  const reconnect = () => {
    console.log('🔄 Manual reconnect requested');
    connect();
  };

  useEffect(() => {
    // Only connect if reportId is valid
    if (reportId && reportId !== 'undefined' && reportId.trim() !== '') {
      connect();
    } else {
      console.log('⏳ Waiting for valid reportId...');
    }

    return () => {
      console.log('🧹 Cleaning up SSE connection for report:', reportId);
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
        eventSourceRef.current = null;
      }
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
        reconnectTimeoutRef.current = null;
      }
      setIsConnected(false);
      setError(null);
    };
  }, [reportId]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      console.log('🧹 Component unmounting, cleaning up SSE');
      isUnmountingRef.current = true;
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
        eventSourceRef.current = null;
      }
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
        reconnectTimeoutRef.current = null;
      }
    };
  }, []);

  return {
    data,
    isConnected,
    error,
    reconnect,
  };
}

// Helper hook for extracting specific progress information
export function useProgressMetrics(progressData: ProgressData | null) {
  if (!progressData || progressData.type !== 'progress') {
    return {
      overallProgress: 0,
      agentProgress: [],
      reportStatus: 'pending',
      isComplete: false,
    };
  }

  const agentTypes = [
    { key: 'lead_research', name: 'Lead Research' },
    { key: 'social_sentiment', name: 'Social Sentiment (X.AI)' },
    { key: 'competitor_analysis', name: 'Competitor Analysis' },
    { key: 'market_positioning', name: 'Market Positioning' },
  ];

  const agentProgress = agentTypes.map(agent => {
    const run = progressData.agentRuns?.find(r => r.agent_type === agent.key);
    return {
      name: agent.name,
      key: agent.key,
      status: run?.status || 'pending',
      progress: run?.metrics?.progress || 0,
      step: run?.metrics?.step || 'pending',
      message: run?.metrics?.message || '',
      startedAt: run?.started_at,
      completedAt: run?.completed_at,
    };
  });

  return {
    overallProgress: progressData.overallProgress || 0,
    agentProgress,
    reportStatus: progressData.report?.status || 'pending',
    isComplete: progressData.report?.status === 'completed',
    isFailed: progressData.report?.status === 'failed',
  };
}
