"use client";
import Link from "next/link";
import { usePathname } from "next/navigation";

import { ModeToggle } from "./mode-toggle";

export default function Header() {
  const pathname = usePathname();
  
  const links = [
    { to: "/", label: "Home" },
    { to: "/dashboard", label: "Dashboard" },
    { to: "/reports", label: "Reports" },
  ];

  return (
    <div>
      <div className="flex flex-row items-center justify-between px-4 py-3">
        <div className="flex items-center gap-8">
          <Link href="/" className="text-xl font-bold">
            CMA
          </Link>
          <nav className="flex gap-6">
            {links.map(({ to, label }) => {
              const isActive = pathname === to;
              return (
                <Link 
                  key={to} 
                  href={to}
                  className={`text-sm font-medium transition-colors hover:text-primary ${
                    isActive 
                      ? "text-foreground" 
                      : "text-muted-foreground"
                  }`}
                >
                  {label}
                </Link>
              );
            })}
          </nav>
        </div>
        <div className="flex items-center gap-2">
          <ModeToggle />
        </div>
      </div>
      <hr />
    </div>
  );
}
