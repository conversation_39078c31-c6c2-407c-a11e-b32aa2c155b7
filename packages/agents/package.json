{"name": "@cma/agents", "version": "0.1.0", "private": true, "main": "src/index.ts", "types": "src/index.ts", "scripts": {"dev": "tsc --watch", "build": "tsc", "check-types": "tsc --noEmit"}, "dependencies": {"@cma/ai": "workspace:*", "@cma/database": "workspace:*", "@mendable/firecrawl-js": "^1.29.1", "exa-js": "^1.8.19", "viem": "^2.31.6", "zod": "^3.25.67"}, "devDependencies": {"@types/node": "^20", "typescript": "^5"}}