import { z } from 'zod';
import { supabase } from '@cma/database';
import { VectorEmbeddingsService } from './vector-embeddings';
import { FirecrawlService } from './integrations/firecrawl';
import { ExaService } from './integrations/exa';
import { SocialMediaService } from './integrations/social-media';
import { BlockchainDataService } from './integrations/blockchain-data';

const DataSourceSchema = z.object({
  id: z.string(),
  project_id: z.string(),
  source_type: z.enum(['website', 'twitter', 'github', 'telegram', 'discord', 'news', 'blockchain']),
  source_url: z.string(),
  status: z.enum(['pending', 'processing', 'completed', 'failed']),
  metadata: z.record(z.any()).optional(),
  last_updated: z.string().optional(),
  error_message: z.string().optional(),
});

export type DataSource = z.infer<typeof DataSourceSchema>;

export interface PipelineConfig {
  deduplication: boolean;
  enrichment: boolean;
  vectorization: boolean;
  batch_size: number;
  retry_attempts: number;
  rate_limit_delay: number;
}

export interface ProcessingResult {
  success: boolean;
  processed_count: number;
  failed_count: number;
  deduplicated_count: number;
  enriched_count: number;
  vectorized_count: number;
  errors: string[];
}

export class DataIngestionPipeline {
  private vectorService: VectorEmbeddingsService;
  private firecrawl: FirecrawlService;
  private exa: ExaService;
  private socialMedia: SocialMediaService;
  private blockchain: BlockchainDataService;

  private defaultConfig: PipelineConfig = {
    deduplication: true,
    enrichment: true,
    vectorization: true,
    batch_size: 10,
    retry_attempts: 3,
    rate_limit_delay: 1000,
  };

  constructor(config?: Partial<PipelineConfig>) {
    this.vectorService = new VectorEmbeddingsService();
    this.firecrawl = new FirecrawlService();
    this.exa = new ExaService();
    this.socialMedia = new SocialMediaService();
    this.blockchain = new BlockchainDataService();
    
    if (config) {
      this.defaultConfig = { ...this.defaultConfig, ...config };
    }
  }

  async processProject(projectId: string, dataSources: DataSource[]): Promise<ProcessingResult> {
    const result: ProcessingResult = {
      success: false,
      processed_count: 0,
      failed_count: 0,
      deduplicated_count: 0,
      enriched_count: 0,
      vectorized_count: 0,
      errors: [],
    };

    try {
      // Process data sources in batches
      const batches = this.createBatches(dataSources, this.defaultConfig.batch_size);
      
      for (const batch of batches) {
        const batchResult = await this.processBatch(projectId, batch);
        
        result.processed_count += batchResult.processed_count;
        result.failed_count += batchResult.failed_count;
        result.deduplicated_count += batchResult.deduplicated_count;
        result.enriched_count += batchResult.enriched_count;
        result.vectorized_count += batchResult.vectorized_count;
        result.errors.push(...batchResult.errors);
        
        // Rate limiting between batches
        await this.delay(this.defaultConfig.rate_limit_delay);
      }

      result.success = result.failed_count === 0 || result.processed_count > 0;
      
      // Update project metadata
      await this.updateProjectIngestionStats(projectId, result);
      
      return result;

    } catch (error) {
      console.error('Pipeline processing error:', error);
      result.errors.push(error instanceof Error ? error.message : 'Unknown pipeline error');
      return result;
    }
  }

  private async processBatch(projectId: string, batch: DataSource[]): Promise<ProcessingResult> {
    const result: ProcessingResult = {
      success: true,
      processed_count: 0,
      failed_count: 0,
      deduplicated_count: 0,
      enriched_count: 0,
      vectorized_count: 0,
      errors: [],
    };

    const processPromises = batch.map(async (dataSource) => {
      try {
        await this.updateDataSourceStatus(dataSource.id, 'processing');
        
        // Step 1: Extract raw data
        const rawData = await this.extractData(dataSource);
        if (!rawData) {
          throw new Error('Failed to extract data');
        }

        // Step 2: Standardize data format
        const standardizedData = await this.standardizeData(rawData, dataSource);

        // Step 3: Deduplication (if enabled)
        let deduplicatedData = standardizedData;
        if (this.defaultConfig.deduplication) {
          deduplicatedData = await this.deduplicateData(projectId, standardizedData, dataSource);
          if (deduplicatedData.length < standardizedData.length) {
            result.deduplicated_count += standardizedData.length - deduplicatedData.length;
          }
        }

        // Step 4: Enrichment (if enabled)
        let enrichedData = deduplicatedData;
        if (this.defaultConfig.enrichment) {
          enrichedData = await this.enrichData(deduplicatedData, dataSource);
          if (enrichedData.some(item => item.enriched)) {
            result.enriched_count += enrichedData.filter(item => item.enriched).length;
          }
        }

        // Step 5: Store processed data
        await this.storeProcessedData(projectId, enrichedData, dataSource);

        // Step 6: Vectorization (if enabled)
        if (this.defaultConfig.vectorization) {
          const vectorizedCount = await this.vectorizeData(projectId, enrichedData, dataSource);
          result.vectorized_count += vectorizedCount;
        }

        await this.updateDataSourceStatus(dataSource.id, 'completed');
        result.processed_count++;

      } catch (error) {
        console.error(`Error processing data source ${dataSource.id}:`, error);
        await this.updateDataSourceStatus(
          dataSource.id, 
          'failed', 
          error instanceof Error ? error.message : 'Unknown error'
        );
        result.failed_count++;
        result.errors.push(`${dataSource.source_type}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    });

    await Promise.all(processPromises);
    return result;
  }

  private async extractData(dataSource: DataSource): Promise<any> {
    switch (dataSource.source_type) {
      case 'website':
        return await this.extractWebsiteData(dataSource);
      case 'twitter':
        return await this.extractTwitterData(dataSource);
      case 'github':
        return await this.extractGithubData(dataSource);
      case 'telegram':
        return await this.extractTelegramData(dataSource);
      case 'discord':
        return await this.extractDiscordData(dataSource);
      case 'news':
        return await this.extractNewsData(dataSource);
      case 'blockchain':
        return await this.extractBlockchainData(dataSource);
      default:
        throw new Error(`Unsupported source type: ${dataSource.source_type}`);
    }
  }

  private async extractWebsiteData(dataSource: DataSource): Promise<any> {
    const result = await this.firecrawl.scrapeUrl(dataSource.source_url);
    if (!result.success) {
      throw new Error('Failed to scrape website');
    }
    
    return {
      type: 'website',
      url: dataSource.source_url,
      content: result.data.markdown,
      metadata: result.data.metadata,
      timestamp: new Date().toISOString(),
    };
  }

  private async extractTwitterData(dataSource: DataSource): Promise<any> {
    const username = this.extractUsernameFromUrl(dataSource.source_url);
    const profile = await this.socialMedia.getTwitterProfile(username);
    const tweets = await this.socialMedia.getTwitterTweets(username, 20);
    
    return {
      type: 'twitter',
      username: username,
      profile: profile.data,
      tweets: tweets.tweets || [],
      timestamp: new Date().toISOString(),
    };
  }

  private async extractGithubData(dataSource: DataSource): Promise<any> {
    const result = await this.firecrawl.scrapeUrl(dataSource.source_url);
    if (!result.success) {
      throw new Error('Failed to scrape GitHub repository');
    }
    
    return {
      type: 'github',
      url: dataSource.source_url,
      content: result.data.markdown,
      metadata: result.data.metadata,
      timestamp: new Date().toISOString(),
    };
  }

  private async extractTelegramData(dataSource: DataSource): Promise<any> {
    const username = this.extractUsernameFromUrl(dataSource.source_url);
    const channelInfo = await this.socialMedia.getTelegramChannelInfo(username);
    
    return {
      type: 'telegram',
      username: username,
      channel_info: channelInfo.data,
      timestamp: new Date().toISOString(),
    };
  }

  private async extractDiscordData(dataSource: DataSource): Promise<any> {
    const serverId = this.extractServerIdFromUrl(dataSource.source_url);
    const serverInfo = await this.socialMedia.getDiscordServerInfo(serverId);
    
    return {
      type: 'discord',
      server_id: serverId,
      server_info: serverInfo.data,
      timestamp: new Date().toISOString(),
    };
  }

  private async extractNewsData(dataSource: DataSource): Promise<any> {
    // Use Exa to search for news articles
    const searchQuery = dataSource.metadata?.search_query || 'news';
    const result = await this.exa.searchNews(searchQuery);
    
    return {
      type: 'news',
      query: searchQuery,
      articles: result.news || [],
      timestamp: new Date().toISOString(),
    };
  }

  private async extractBlockchainData(dataSource: DataSource): Promise<any> {
    const protocol = dataSource.metadata?.protocol;
    if (!protocol) {
      throw new Error('Protocol name required for blockchain data');
    }
    
    const protocolData = await this.blockchain.getDeFiLlamaProtocol(protocol);
    const tokenPrice = await this.blockchain.getTokenPrice(protocol);
    
    return {
      type: 'blockchain',
      protocol: protocol,
      tvl_data: protocolData.data,
      price_data: tokenPrice.data,
      timestamp: new Date().toISOString(),
    };
  }

  private async standardizeData(rawData: any, dataSource: DataSource): Promise<any[]> {
    // Convert raw data into standardized format
    const standardized = [];
    
    switch (dataSource.source_type) {
      case 'website':
        standardized.push({
          id: `${dataSource.project_id}_website_${Date.now()}`,
          content: rawData.content,
          title: rawData.metadata?.title,
          url: rawData.url,
          source_type: 'website',
          metadata: rawData.metadata,
          timestamp: rawData.timestamp,
        });
        break;
        
      case 'twitter':
        rawData.tweets.forEach((tweet: any, index: number) => {
          standardized.push({
            id: `${dataSource.project_id}_twitter_${tweet.id || index}`,
            content: tweet.text,
            title: `Tweet by @${rawData.username}`,
            url: `https://twitter.com/${rawData.username}/status/${tweet.id}`,
            source_type: 'twitter',
            metadata: {
              username: rawData.username,
              likes: tweet.like_count,
              retweets: tweet.retweet_count,
              created_at: tweet.created_at,
            },
            timestamp: rawData.timestamp,
          });
        });
        break;
        
      case 'github':
        standardized.push({
          id: `${dataSource.project_id}_github_${Date.now()}`,
          content: rawData.content,
          title: rawData.metadata?.title,
          url: rawData.url,
          source_type: 'github',
          metadata: rawData.metadata,
          timestamp: rawData.timestamp,
        });
        break;
        
      case 'news':
        rawData.articles.forEach((article: any, index: number) => {
          standardized.push({
            id: `${dataSource.project_id}_news_${index}`,
            content: article.content,
            title: article.title,
            url: article.url,
            source_type: 'news',
            metadata: {
              published_date: article.published_date,
              source: article.source,
            },
            timestamp: rawData.timestamp,
          });
        });
        break;
        
      default:
        // Generic standardization
        standardized.push({
          id: `${dataSource.project_id}_${dataSource.source_type}_${Date.now()}`,
          content: JSON.stringify(rawData),
          title: `${dataSource.source_type} data`,
          url: dataSource.source_url,
          source_type: dataSource.source_type,
          metadata: rawData,
          timestamp: new Date().toISOString(),
        });
    }
    
    return standardized;
  }

  private async deduplicateData(projectId: string, data: any[], dataSource: DataSource): Promise<any[]> {
    // Check for existing data with similar content
    const deduplicated = [];
    
    for (const item of data) {
      const exists = await this.checkDataExists(projectId, item);
      if (!exists) {
        deduplicated.push(item);
      }
    }
    
    return deduplicated;
  }

  private async checkDataExists(projectId: string, item: any): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from('data_sources')
        .select('id')
        .eq('project_id', projectId)
        .eq('source_type', item.source_type)
        .eq('source_url', item.url)
        .limit(1);

      if (error) {
        console.error('Error checking data existence:', error);
        return false;
      }

      return data && data.length > 0;
    } catch (error) {
      console.error('Error checking data existence:', error);
      return false;
    }
  }

  private async enrichData(data: any[], dataSource: DataSource): Promise<any[]> {
    // Enrich data with additional context using Exa search
    const enriched = [];
    
    for (const item of data) {
      try {
        // Search for related content
        const searchResult = await this.exa.searchWeb(`${item.title} ${dataSource.project_id}`, {
          numResults: 3,
          maxCharacters: 500,
        });
        
        const enrichedItem = {
          ...item,
          enriched: true,
          related_content: searchResult.results || [],
          enrichment_timestamp: new Date().toISOString(),
        };
        
        enriched.push(enrichedItem);
        
        // Rate limiting for enrichment
        await this.delay(500);
        
      } catch (error) {
        console.error('Error enriching data:', error);
        enriched.push({ ...item, enriched: false });
      }
    }
    
    return enriched;
  }

  private async storeProcessedData(projectId: string, data: any[], dataSource: DataSource): Promise<void> {
    // Store processed data in Supabase
    const records = data.map(item => ({
      id: item.id,
      project_id: projectId,
      source_type: dataSource.source_type,
      source_url: dataSource.source_url,
      content: item.content,
      title: item.title,
      metadata: item.metadata || {},
      enriched: item.enriched || false,
      processed_at: new Date().toISOString(),
    }));

    const { error } = await supabase
      .from('data_sources')
      .upsert(records);

    if (error) {
      throw new Error(`Failed to store processed data: ${error.message}`);
    }
  }

  private async vectorizeData(projectId: string, data: any[], dataSource: DataSource): Promise<number> {
    // Create vector embeddings for the processed data
    let vectorizedCount = 0;
    
    for (const item of data) {
      try {
        await this.vectorService.storeEmbedding({
          id: item.id,
          content: item.content,
          source_type: this.mapSourceType(dataSource.source_type),
          source_url: item.url,
          project_id: projectId,
          metadata: item.metadata,
        });
        
        vectorizedCount++;
      } catch (error) {
        console.error('Error vectorizing data:', error);
      }
    }
    
    return vectorizedCount;
  }

  private async updateDataSourceStatus(
    dataSourceId: string, 
    status: 'pending' | 'processing' | 'completed' | 'failed',
    errorMessage?: string
  ): Promise<void> {
    const updateData: any = {
      status,
      last_updated: new Date().toISOString(),
    };
    
    if (errorMessage) {
      updateData.error_message = errorMessage;
    }

    const { error } = await supabase
      .from('data_source_status')
      .upsert({
        id: dataSourceId,
        ...updateData,
      });

    if (error) {
      console.error('Error updating data source status:', error);
    }
  }

  private async updateProjectIngestionStats(projectId: string, result: ProcessingResult): Promise<void> {
    const stats = {
      project_id: projectId,
      last_ingestion: new Date().toISOString(),
      total_processed: result.processed_count,
      total_failed: result.failed_count,
      deduplicated_count: result.deduplicated_count,
      enriched_count: result.enriched_count,
      vectorized_count: result.vectorized_count,
    };

    const { error } = await supabase
      .from('project_ingestion_stats')
      .upsert(stats);

    if (error) {
      console.error('Error updating project ingestion stats:', error);
    }
  }

  private createBatches<T>(items: T[], batchSize: number): T[][] {
    const batches: T[][] = [];
    for (let i = 0; i < items.length; i += batchSize) {
      batches.push(items.slice(i, i + batchSize));
    }
    return batches;
  }

  private extractUsernameFromUrl(url: string): string {
    // Extract username from various social media URLs
    const patterns = [
      /twitter\.com\/([^\/\?]+)/,
      /x\.com\/([^\/\?]+)/,
      /t\.me\/([^\/\?]+)/,
    ];
    
    for (const pattern of patterns) {
      const match = url.match(pattern);
      if (match) {
        return match[1];
      }
    }
    
    throw new Error('Could not extract username from URL');
  }

  private extractServerIdFromUrl(url: string): string {
    // Extract Discord server ID from invite URLs
    const match = url.match(/discord\.gg\/([^\/\?]+)/);
    if (match) {
      return match[1];
    }
    
    throw new Error('Could not extract server ID from Discord URL');
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Public methods for manual operations
  
  async retryFailedDataSources(projectId: string): Promise<ProcessingResult> {
    // Get failed data sources
    const { data: failedSources, error } = await supabase
      .from('data_source_status')
      .select('*')
      .eq('status', 'failed');

    if (error || !failedSources) {
      throw new Error('Failed to get failed data sources');
    }

    const dataSources: DataSource[] = failedSources.map(source => ({
      id: source.id,
      project_id: projectId,
      source_type: source.source_type,
      source_url: source.source_url,
      status: 'pending',
      metadata: source.metadata || {},
    }));

    return await this.processProject(projectId, dataSources);
  }

  async getIngestionStats(projectId: string): Promise<any> {
    const { data, error } = await supabase
      .from('project_ingestion_stats')
      .select('*')
      .eq('project_id', projectId)
      .single();

    if (error) {
      return null;
    }

    return data;
  }

  async cleanupOldData(projectId: string, daysOld: number = 30): Promise<number> {
    const cutoffDate = new Date(Date.now() - daysOld * 24 * 60 * 60 * 1000).toISOString();
    
    const { data, error } = await supabase
      .from('data_sources')
      .delete()
      .eq('project_id', projectId)
      .lt('processed_at', cutoffDate)
      .select('id');

    if (error) {
      throw new Error(`Failed to cleanup old data: ${error.message}`);
    }

    return data?.length || 0;
  }

  private mapSourceType(sourceType: string): 'twitter' | 'website' | 'github' | 'documentation' | 'news' | 'analysis' {
    switch (sourceType) {
      case 'discord':
      case 'telegram':
        return 'documentation';
      case 'blockchain':
        return 'analysis';
      default:
        return sourceType as 'twitter' | 'website' | 'github' | 'documentation' | 'news' | 'analysis';
    }
  }
}