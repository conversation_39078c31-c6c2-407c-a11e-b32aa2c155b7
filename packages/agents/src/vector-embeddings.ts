import { createOpenAI } from '@ai-sdk/openai';
import { embed, embedMany } from 'ai';
import { supabase } from '@cma/database';
import { z } from 'zod';

const EmbeddingRecordSchema = z.object({
  id: z.string(),
  content: z.string(),
  embedding: z.array(z.number()),
  metadata: z.record(z.any()).optional(),
  source_type: z.enum(['website', 'twitter', 'github', 'documentation', 'news', 'analysis']),
  source_url: z.string().optional(),
  project_id: z.string().optional(),
  created_at: z.string().optional(),
});

export type EmbeddingRecord = z.infer<typeof EmbeddingRecordSchema>;

export interface SearchOptions {
  threshold?: number;
  limit?: number;
  source_type?: string;
  project_id?: string;
  metadata_filter?: Record<string, any>;
}

export interface SearchResult {
  content: string;
  similarity: number;
  metadata?: Record<string, any>;
  source_type: string;
  source_url?: string;
  project_id?: string;
}

export class VectorEmbeddingsService {
  private openai = createOpenAI({ apiKey: process.env.OPENAI_API_KEY || '' });
  private model = this.openai.embedding('text-embedding-3-small');

  async createEmbedding(text: string): Promise<number[]> {
    try {
      const { embedding } = await embed({
        model: this.model,
        value: text,
      });
      
      return embedding;
    } catch (error) {
      console.error('Error creating embedding:', error);
      throw new Error('Failed to create embedding');
    }
  }

  async createBatchEmbeddings(texts: string[]): Promise<number[][]> {
    try {
      const { embeddings } = await embedMany({
        model: this.model,
        values: texts,
      });
      
      return embeddings;
    } catch (error) {
      console.error('Error creating batch embeddings:', error);
      throw new Error('Failed to create batch embeddings');
    }
  }

  async storeEmbedding(record: Omit<EmbeddingRecord, 'embedding' | 'created_at'>): Promise<string> {
    try {
      // Create embedding for the content
      const embedding = await this.createEmbedding(record.content);
      
      // Store in Supabase
      const { data, error } = await supabase
        .from('embeddings')
        .insert({
          id: record.id,
          content: record.content,
          embedding: embedding,
          metadata: record.metadata || {},
          source_type: record.source_type,
          source_url: record.source_url,
          project_id: record.project_id,
          created_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (error) {
        throw new Error(`Database error: ${error.message}`);
      }

      return data.id;
    } catch (error) {
      console.error('Error storing embedding:', error);
      throw new Error('Failed to store embedding');
    }
  }

  async storeBatchEmbeddings(records: Omit<EmbeddingRecord, 'embedding' | 'created_at'>[]): Promise<string[]> {
    try {
      // Create embeddings for all content
      const contents = records.map(r => r.content);
      const embeddings = await this.createBatchEmbeddings(contents);
      
      // Prepare records for insertion
      const embeddingRecords = records.map((record, index) => ({
        id: record.id,
        content: record.content,
        embedding: embeddings[index],
        metadata: record.metadata || {},
        source_type: record.source_type,
        source_url: record.source_url,
        project_id: record.project_id,
        created_at: new Date().toISOString(),
      }));

      // Store in Supabase
      const { data, error } = await supabase
        .from('embeddings')
        .insert(embeddingRecords)
        .select('id');

      if (error) {
        throw new Error(`Database error: ${error.message}`);
      }

      return data.map(d => d.id);
    } catch (error) {
      console.error('Error storing batch embeddings:', error);
      throw new Error('Failed to store batch embeddings');
    }
  }

  async similaritySearch(
    query: string,
    options: SearchOptions = {}
  ): Promise<SearchResult[]> {
    try {
      // Create embedding for the query
      const queryEmbedding = await this.createEmbedding(query);
      
      // Build the query
      let supabaseQuery = supabase.rpc('match_embeddings', {
        query_embedding: queryEmbedding,
        match_threshold: options.threshold || 0.7,
        match_count: options.limit || 10,
      });

      // Apply filters if provided
      if (options.source_type) {
        supabaseQuery = supabaseQuery.eq('source_type', options.source_type);
      }
      
      if (options.project_id) {
        supabaseQuery = supabaseQuery.eq('project_id', options.project_id);
      }

      const { data, error } = await supabaseQuery;

      if (error) {
        throw new Error(`Search error: ${error.message}`);
      }

      return data.map((row: any) => ({
        content: row.content,
        similarity: row.similarity,
        metadata: row.metadata,
        source_type: row.source_type,
        source_url: row.source_url,
        project_id: row.project_id,
      }));
    } catch (error) {
      console.error('Error in similarity search:', error);
      throw new Error('Failed to perform similarity search');
    }
  }

  async semanticSearch(
    projectId: string,
    query: string,
    options: SearchOptions = {}
  ): Promise<SearchResult[]> {
    return this.similaritySearch(query, {
      ...options,
      project_id: projectId,
    });
  }

  async findSimilarContent(
    content: string,
    options: SearchOptions = {}
  ): Promise<SearchResult[]> {
    return this.similaritySearch(content, {
      ...options,
      threshold: options.threshold || 0.8, // Higher threshold for similarity
    });
  }

  async indexWebsiteContent(
    projectId: string,
    websiteData: any
  ): Promise<string[]> {
    try {
      const chunks = this.chunkContent(websiteData.content || '', 1000);
      
      const records = chunks.map((chunk, index) => ({
        id: `${projectId}_website_${index}`,
        content: chunk,
        source_type: 'website' as const,
        source_url: websiteData.url,
        project_id: projectId,
        metadata: {
          title: websiteData.title,
          chunk_index: index,
          total_chunks: chunks.length,
        },
      }));

      return await this.storeBatchEmbeddings(records);
    } catch (error) {
      console.error('Error indexing website content:', error);
      throw new Error('Failed to index website content');
    }
  }

  async indexTwitterContent(
    projectId: string,
    twitterData: any
  ): Promise<string[]> {
    try {
      const records = twitterData.tweets.map((tweet: any, index: number) => ({
        id: `${projectId}_twitter_${tweet.id || index}`,
        content: tweet.text,
        source_type: 'twitter' as const,
        source_url: `https://twitter.com/${twitterData.username}/status/${tweet.id}`,
        project_id: projectId,
        metadata: {
          username: twitterData.username,
          created_at: tweet.created_at,
          likes: tweet.like_count,
          retweets: tweet.retweet_count,
          replies: tweet.reply_count,
        },
      }));

      return await this.storeBatchEmbeddings(records);
    } catch (error) {
      console.error('Error indexing Twitter content:', error);
      throw new Error('Failed to index Twitter content');
    }
  }

  async indexGithubContent(
    projectId: string,
    githubData: any
  ): Promise<string[]> {
    try {
      const chunks = this.chunkContent(githubData.content || '', 1500);
      
      const records = chunks.map((chunk, index) => ({
        id: `${projectId}_github_${index}`,
        content: chunk,
        source_type: 'github' as const,
        source_url: githubData.url,
        project_id: projectId,
        metadata: {
          repository: githubData.repository,
          file_path: githubData.file_path,
          chunk_index: index,
          total_chunks: chunks.length,
        },
      }));

      return await this.storeBatchEmbeddings(records);
    } catch (error) {
      console.error('Error indexing GitHub content:', error);
      throw new Error('Failed to index GitHub content');
    }
  }

  async indexNewsContent(
    projectId: string,
    newsArticles: any[]
  ): Promise<string[]> {
    try {
      const records = newsArticles.map((article, index) => ({
        id: `${projectId}_news_${index}`,
        content: `${article.title}\n\n${article.content}`,
        source_type: 'news' as const,
        source_url: article.url,
        project_id: projectId,
        metadata: {
          title: article.title,
          published_date: article.published_date,
          source: article.source,
          sentiment: article.sentiment,
        },
      }));

      return await this.storeBatchEmbeddings(records);
    } catch (error) {
      console.error('Error indexing news content:', error);
      throw new Error('Failed to index news content');
    }
  }

  async indexAnalysisResults(
    projectId: string,
    agentName: string,
    analysisData: any
  ): Promise<string> {
    try {
      const content = this.formatAnalysisForEmbedding(analysisData);
      
      const record = {
        id: `${projectId}_analysis_${agentName}`,
        content: content,
        source_type: 'analysis' as const,
        project_id: projectId,
        metadata: {
          agent_name: agentName,
          analysis_type: analysisData.type || 'general',
          confidence_score: analysisData.confidence_score,
          created_at: new Date().toISOString(),
        },
      };

      const [id] = await this.storeBatchEmbeddings([record]);
      return id;
    } catch (error) {
      console.error('Error indexing analysis results:', error);
      throw new Error('Failed to index analysis results');
    }
  }

  async getProjectEmbeddings(projectId: string): Promise<EmbeddingRecord[]> {
    try {
      const { data, error } = await supabase
        .from('embeddings')
        .select('*')
        .eq('project_id', projectId)
        .order('created_at', { ascending: false });

      if (error) {
        throw new Error(`Database error: ${error.message}`);
      }

      return data || [];
    } catch (error) {
      console.error('Error getting project embeddings:', error);
      throw new Error('Failed to get project embeddings');
    }
  }

  async deleteProjectEmbeddings(projectId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('embeddings')
        .delete()
        .eq('project_id', projectId);

      if (error) {
        throw new Error(`Database error: ${error.message}`);
      }
    } catch (error) {
      console.error('Error deleting project embeddings:', error);
      throw new Error('Failed to delete project embeddings');
    }
  }

  async updateEmbedding(
    id: string,
    updates: Partial<Omit<EmbeddingRecord, 'id' | 'embedding'>>
  ): Promise<void> {
    try {
      let updateData: any = { ...updates };
      
      // If content is being updated, recalculate embedding
      if (updates.content) {
        updateData.embedding = await this.createEmbedding(updates.content);
      }

      const { error } = await supabase
        .from('embeddings')
        .update(updateData)
        .eq('id', id);

      if (error) {
        throw new Error(`Database error: ${error.message}`);
      }
    } catch (error) {
      console.error('Error updating embedding:', error);
      throw new Error('Failed to update embedding');
    }
  }

  async getEmbeddingStats(projectId?: string): Promise<any> {
    try {
      let query = supabase
        .from('embeddings')
        .select('source_type, created_at');

      if (projectId) {
        query = query.eq('project_id', projectId);
      }

      const { data, error } = await query;

      if (error) {
        throw new Error(`Database error: ${error.message}`);
      }

      const stats = {
        total_embeddings: data.length,
        by_source_type: {} as Record<string, number>,
        recent_additions: 0,
      };

      const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);

      data.forEach(embedding => {
        // Count by source type
        stats.by_source_type[embedding.source_type] = 
          (stats.by_source_type[embedding.source_type] || 0) + 1;
        
        // Count recent additions
        if (new Date(embedding.created_at) > oneDayAgo) {
          stats.recent_additions++;
        }
      });

      return stats;
    } catch (error) {
      console.error('Error getting embedding stats:', error);
      throw new Error('Failed to get embedding stats');
    }
  }

  private chunkContent(content: string, maxChunkSize: number): string[] {
    const chunks: string[] = [];
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);
    
    let currentChunk = '';
    
    for (const sentence of sentences) {
      const trimmedSentence = sentence.trim();
      
      if ((currentChunk + trimmedSentence).length <= maxChunkSize) {
        currentChunk += (currentChunk ? '. ' : '') + trimmedSentence;
      } else {
        if (currentChunk) {
          chunks.push(currentChunk + '.');
        }
        currentChunk = trimmedSentence;
      }
    }
    
    if (currentChunk) {
      chunks.push(currentChunk + '.');
    }
    
    return chunks.filter(chunk => chunk.length > 20); // Filter out very short chunks
  }

  private formatAnalysisForEmbedding(analysisData: any): string {
    // Convert analysis data to searchable text format
    const sections = [];
    
    if (analysisData.summary) {
      sections.push(`Summary: ${analysisData.summary}`);
    }
    
    if (analysisData.key_findings) {
      sections.push(`Key Findings: ${JSON.stringify(analysisData.key_findings)}`);
    }
    
    if (analysisData.recommendations) {
      sections.push(`Recommendations: ${JSON.stringify(analysisData.recommendations)}`);
    }
    
    if (analysisData.metrics) {
      sections.push(`Metrics: ${JSON.stringify(analysisData.metrics)}`);
    }
    
    return sections.join('\n\n');
  }
}