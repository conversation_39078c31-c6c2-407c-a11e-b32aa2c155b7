import { BaseAgent } from './base';
import type { <PERSON>Context, AgentResult } from './base';
import { SYSTEM_PROMPTS } from '@cma/ai';
import { z } from 'zod';

const CompetitorAnalysisSchema = z.object({
  direct_competitors: z.array(z.object({
    name: z.string(),
    description: z.string(),
    market_cap: z.number().optional(),
    tvl: z.number().optional(),
    users: z.number().optional(),
    strengths: z.array(z.string()),
    weaknesses: z.array(z.string()),
    market_share: z.number(),
    differentiation: z.string(),
  })),
  indirect_competitors: z.array(z.object({
    name: z.string(),
    description: z.string(),
    market_cap: z.number().optional(),
    overlap_areas: z.array(z.string()),
    threat_level: z.enum(['high', 'medium', 'low']),
  })),
  market_positioning: z.object({
    quadrant: z.enum(['leader', 'challenger', 'follower', 'niche']),
    market_share_rank: z.number(),
    competitive_advantages: z.array(z.string()),
    competitive_disadvantages: z.array(z.string()),
    moat_strength: z.enum(['strong', 'moderate', 'weak']),
  }),
  feature_comparison: z.object({
    features: z.array(z.object({
      name: z.string(),
      project_support: z.boolean(),
      competitor_support: z.record(z.boolean()),
      importance: z.enum(['critical', 'important', 'nice-to-have']),
    })),
    feature_gap_analysis: z.array(z.string()),
    innovation_opportunities: z.array(z.string()),
  }),
  partnership_ecosystem: z.object({
    integration_partners: z.array(z.string()),
    strategic_alliances: z.array(z.string()),
    competitor_partnerships: z.record(z.array(z.string())),
    partnership_opportunities: z.array(z.string()),
  }),
  market_trends: z.object({
    sector_growth_rate: z.number(),
    emerging_trends: z.array(z.string()),
    technology_shifts: z.array(z.string()),
    regulatory_impact: z.string(),
    market_consolidation: z.boolean(),
  }),
  competitive_threats: z.array(z.object({
    threat: z.string(),
    probability: z.enum(['high', 'medium', 'low']),
    impact: z.enum(['high', 'medium', 'low']),
    mitigation_strategy: z.string(),
  })),
  opportunities: z.array(z.object({
    opportunity: z.string(),
    market_size: z.number().optional(),
    timeline: z.string(),
    requirements: z.array(z.string()),
  })),
});

export class CompetitorAnalysisAgent extends BaseAgent {
  constructor() {
    super('competitor_analysis', SYSTEM_PROMPTS.COMPETITOR_ANALYSIS);
  }

  async execute(context: AgentContext, input: any): Promise<AgentResult> {
    console.log('🚀 CompetitorAnalysis: Starting competitor analysis execution');
    console.log('📊 CompetitorAnalysis: Input data:', {
      projectName: input?.project_metadata?.name,
      projectType: input?.project_metadata?.type,
      reportId: context.reportId
    });

    try {
      console.log('🔄 CompetitorAnalysis: Updating progress - initializing');
      await this.updateProgress(context.reportId, {
        step: 'initializing',
        progress: 5,
        status: 'running',
        message: 'Initializing competitor analysis',
      });

      const project = input.project || input.initialContext?.projectMetadata;
      const researchScope = input.researchScope?.competitorAnalysis || {};

      console.log('📋 CompetitorAnalysis: Project details:', {
        name: project?.name,
        type: project?.type,
        scope: Object.keys(researchScope)
      });

      // Step 1: Identify direct competitors
      console.log('🔄 CompetitorAnalysis: Step 1 - Identifying direct competitors');
      await this.updateProgress(context.reportId, {
        step: 'identifying_direct_competitors',
        progress: 15,
        status: 'running',
        message: 'Identifying direct competitors',
      });

      const directCompetitors = await this.identifyDirectCompetitors(project, researchScope);
      console.log(`✅ CompetitorAnalysis: Found ${directCompetitors.length} direct competitors:`,
        directCompetitors.map(c => c.name));

      // Step 2: Identify indirect competitors
      console.log('🔄 CompetitorAnalysis: Step 2 - Identifying indirect competitors');
      await this.updateProgress(context.reportId, {
        step: 'identifying_indirect_competitors',
        progress: 25,
        status: 'running',
        message: 'Identifying indirect competitors and market adjacencies',
      });

      const indirectCompetitors = await this.identifyIndirectCompetitors(project, researchScope);
      console.log(`✅ CompetitorAnalysis: Found ${indirectCompetitors.length} indirect competitors:`,
        indirectCompetitors.map(c => c.name));

      // Step 3: Analyze market positioning
      await this.updateProgress(context.reportId, {
        step: 'analyzing_positioning',
        progress: 40,
        status: 'running',
        message: 'Analyzing market positioning and competitive landscape',
      });

      const marketPositioning = await this.analyzeMarketPositioning(project, directCompetitors);

      // Step 4: Conduct feature comparison
      await this.updateProgress(context.reportId, {
        step: 'comparing_features',
        progress: 55,
        status: 'running',
        message: 'Conducting comprehensive feature comparison',
      });

      const featureComparison = await this.conductFeatureComparison(project, directCompetitors);

      // Step 5: Analyze partnership ecosystem
      await this.updateProgress(context.reportId, {
        step: 'analyzing_partnerships',
        progress: 70,
        status: 'running',
        message: 'Analyzing partnership ecosystem and strategic alliances',
      });

      const partnershipEcosystem = await this.analyzePartnershipEcosystem(project, directCompetitors);

      // Step 6: Assess market trends and threats
      await this.updateProgress(context.reportId, {
        step: 'assessing_threats',
        progress: 85,
        status: 'running',
        message: 'Assessing competitive threats and market opportunities',
      });

      const marketTrends = await this.assessMarketTrends(project.type);
      const competitiveThreats = await this.identifyCompetitiveThreats(project, directCompetitors);
      const opportunities = await this.identifyOpportunities(project, marketTrends, featureComparison);

      // Step 7: Synthesize comprehensive analysis
      await this.updateProgress(context.reportId, {
        step: 'synthesizing',
        progress: 95,
        status: 'running',
        message: 'Synthesizing competitive intelligence',
      });

      const comprehensiveAnalysis = await this.synthesizeAnalysis({
        direct_competitors: directCompetitors,
        indirect_competitors: indirectCompetitors,
        market_positioning: marketPositioning,
        feature_comparison: featureComparison,
        partnership_ecosystem: partnershipEcosystem,
        market_trends: marketTrends,
        competitive_threats: competitiveThreats,
        opportunities: opportunities,
      });

      await this.updateProgress(context.reportId, {
        step: 'completed',
        progress: 100,
        status: 'completed',
        message: 'Competitor analysis completed successfully',
      });

      const result: AgentResult = {
        success: true,
        data: {
          competitive_landscape: comprehensiveAnalysis,
          summary: await this.generateSummary(comprehensiveAnalysis),
          recommendations: await this.generateRecommendations(comprehensiveAnalysis),
          market_analysis: this.extractMarketAnalysis(comprehensiveAnalysis),
        },
        sources: this.generateSources(directCompetitors, indirectCompetitors),
        metrics: {
          direct_competitors: directCompetitors.length,
          indirect_competitors: indirectCompetitors.length,
          feature_gaps: featureComparison.feature_gap_analysis.length,
          opportunities: opportunities.length,
        },
      };

      await this.cacheData(
        context.projectId,
        'competitor_analysis',
        `analysis_${project.name}`,
        result.data,
        8 // Cache for 8 hours
      );

      await this.logAgentRun(context.reportId, input, result);
      return result;

    } catch (error) {
      const result: AgentResult = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error in competitor analysis',
      };

      await this.logAgentRun(context.reportId, input, result);
      return result;
    }
  }

  private async identifyDirectCompetitors(project: any, researchScope: any): Promise<any[]> {
    // Get competitors from research scope or identify them
    let competitorNames = researchScope.directCompetitors || [];
    
    if (competitorNames.length === 0) {
      competitorNames = await this.discoverCompetitors(project);
    }

    const competitors = [];
    for (const name of competitorNames.slice(0, 5)) { // Limit to top 5
      const competitorData = await this.analyzeCompetitor(name, project.type);
      if (competitorData) {
        competitors.push(competitorData);
      }
      await this.rateLimitWait(300);
    }

    return competitors;
  }

  private async identifyIndirectCompetitors(project: any, researchScope: any): Promise<any[]> {
    let indirectNames = researchScope.indirectCompetitors || [];
    
    if (indirectNames.length === 0) {
      indirectNames = await this.discoverIndirectCompetitors(project);
    }

    const indirectCompetitors = [];
    for (const name of indirectNames.slice(0, 3)) { // Limit to top 3
      const competitorData = await this.analyzeIndirectCompetitor(name, project.type);
      if (competitorData) {
        indirectCompetitors.push(competitorData);
      }
      await this.rateLimitWait(200);
    }

    return indirectCompetitors;
  }

  private async discoverCompetitors(project: any): Promise<string[]> {
    console.log(`🔍 CompetitorAnalysis: Discovering competitors for project type: ${project.type}`);

    // Static competitor mapping - TODO: Make this dynamic with real market data
    console.log('⚠️ CompetitorAnalysis: Using static competitor mapping, should be made dynamic');

    const competitorMap: Record<string, string[]> = {
      defi: ['Uniswap', 'Aave', 'Compound', 'MakerDAO', 'Curve', 'SushiSwap'],
      nft: ['OpenSea', 'Blur', 'LooksRare', 'SuperRare', 'Foundation'],
      gaming: ['Axie Infinity', 'The Sandbox', 'Decentraland', 'Gala Games'],
      infrastructure: ['Chainlink', 'The Graph', 'Polygon', 'Arbitrum', 'Optimism'],
      dao: ['Aragon', 'DAOstack', 'Moloch', 'Colony'],
      other: ['Ethereum', 'Solana', 'Cardano', 'Polkadot'],
    };

    const competitors = competitorMap[project.type] || competitorMap.other;
    console.log(`✅ CompetitorAnalysis: Found ${competitors.length} competitors:`, competitors);

    return competitors;
  }

  private async discoverIndirectCompetitors(project: any): Promise<string[]> {
    // Identify adjacent markets and cross-sector competitors
    const adjacentMap: Record<string, string[]> = {
      defi: ['Traditional Finance', 'Centralized Exchanges', 'Neobanks'],
      nft: ['Traditional Art Markets', 'Gaming Platforms', 'Social Media'],
      gaming: ['Traditional Gaming', 'Mobile Games', 'Social Platforms'],
      infrastructure: ['Cloud Providers', 'CDNs', 'Traditional Infrastructure'],
      dao: ['Corporate Governance', 'Voting Systems', 'Collaboration Tools'],
    };

    return adjacentMap[project.type] || [];
  }

  private async analyzeCompetitor(name: string, projectType: string): Promise<any> {
    // Mock data removed - will integrate with real data sources
    console.log(`🔍 CompetitorAnalysis: Analyzing competitor ${name} (${projectType})`);

    try {
      // TODO: Integrate with real data sources:
      // - DeFiLlama for TVL and protocol data
      // - CoinGecko/CoinMarketCap for market cap
      // - GitHub for development activity
      // - Social media APIs for community metrics

      console.log(`⚠️ CompetitorAnalysis: Mock data removed for ${name}, returning null until API integration`);

      return {
        name,
        description: `${projectType} protocol - data pending API integration`,
        market_cap: null,
        tvl: null,
        users: null,
        strengths: [],
        weaknesses: [],
        market_share: null,
        differentiation: 'Analysis pending real data integration',
        data_source: 'placeholder',
        last_updated: new Date().toISOString(),
      };
    } catch (error) {
      console.error(`❌ CompetitorAnalysis: Error analyzing ${name}:`, error);
      throw error;
    }
  }

  private async analyzeIndirectCompetitor(name: string, projectType: string): Promise<any> {
    return {
      name,
      description: `Traditional ${projectType} alternative`,
      market_cap: Math.floor(Math.random() * 10000000000),
      overlap_areas: ['User acquisition', 'Value proposition'],
      threat_level: ['high', 'medium', 'low'][Math.floor(Math.random() * 3)] as 'high' | 'medium' | 'low',
    };
  }

  private async analyzeMarketPositioning(project: any, competitors: any[]): Promise<any> {
    // Analyze project's position relative to competitors
    const totalMarketShare = competitors.reduce((sum, comp) => sum + comp.market_share, 0);
    const projectMarketShare = Math.max(1, 100 - totalMarketShare);

    let quadrant: 'leader' | 'challenger' | 'follower' | 'niche' = 'follower';
    if (projectMarketShare > 20) quadrant = 'leader';
    else if (projectMarketShare > 10) quadrant = 'challenger';
    else if (projectMarketShare < 3) quadrant = 'niche';

    return {
      quadrant,
      market_share_rank: competitors.length + 1,
      competitive_advantages: await this.identifyAdvantages(project, competitors),
      competitive_disadvantages: await this.identifyDisadvantages(project, competitors),
      moat_strength: this.assessMoatStrength(project),
    };
  }

  private async identifyAdvantages(project: any, competitors: any[]): Promise<string[]> {
    const advantages = [];
    
    if (project.type === 'defi' && project.github) {
      advantages.push('Open source and transparent');
    }
    
    if (project.description?.includes('innovative')) {
      advantages.push('Innovative technology approach');
    }

    advantages.push('Emerging player with growth potential');
    return advantages;
  }

  private async identifyDisadvantages(project: any, competitors: any[]): Promise<string[]> {
    const disadvantages = [];
    
    disadvantages.push('Smaller market share vs established players');
    disadvantages.push('Limited brand recognition');
    
    if (competitors.some(comp => comp.tvl > 1000000000)) {
      disadvantages.push('Lower liquidity than major competitors');
    }

    return disadvantages;
  }

  private assessMoatStrength(project: any): 'strong' | 'moderate' | 'weak' {
    if (project.type === 'infrastructure') return 'moderate';
    if (project.github && project.contracts) return 'moderate';
    return 'weak';
  }

  private async conductFeatureComparison(project: any, competitors: any[]): Promise<any> {
    const coreFeatures = this.getCoreFeatures(project.type);
    const featureMatrix: any[] = [];

    for (const feature of coreFeatures) {
      const competitorSupport: Record<string, boolean> = {};

      console.log(`🔍 CompetitorAnalysis: Analyzing feature ${feature.name} across competitors`);

      for (const competitor of competitors) {
        // Mock data removed - would analyze actual feature support
        console.log(`⚠️ CompetitorAnalysis: Mock feature support removed for ${competitor.name}`);
        competitorSupport[competitor.name] = false; // Default to false until real analysis
      }

      featureMatrix.push({
        name: feature.name,
        project_support: false, // Default to false until real analysis
        competitor_support: competitorSupport,
        importance: feature.importance,
        analysis_status: 'pending_real_data',
      });
    }

    const featureGaps = featureMatrix
      .filter(f => !f.project_support && f.importance === 'critical')
      .map(f => f.name);

    const innovationOps = featureMatrix
      .filter(f => f.project_support && !Object.values(f.competitor_support).some(Boolean))
      .map(f => f.name);

    return {
      features: featureMatrix,
      feature_gap_analysis: featureGaps,
      innovation_opportunities: innovationOps,
    };
  }

  private getCoreFeatures(projectType: string): any[] {
    const featureMap: Record<string, any[]> = {
      defi: [
        { name: 'AMM Trading', importance: 'critical' },
        { name: 'Yield Farming', importance: 'important' },
        { name: 'Governance Token', importance: 'important' },
        { name: 'Flash Loans', importance: 'nice-to-have' },
        { name: 'Cross-chain Support', importance: 'important' },
      ],
      nft: [
        { name: 'Marketplace', importance: 'critical' },
        { name: 'Royalties', importance: 'critical' },
        { name: 'Bulk Operations', importance: 'important' },
        { name: 'Fractionalization', importance: 'nice-to-have' },
      ],
      infrastructure: [
        { name: 'Scalability', importance: 'critical' },
        { name: 'Security', importance: 'critical' },
        { name: 'Developer Tools', importance: 'important' },
        { name: 'Interoperability', importance: 'important' },
      ],
    };

    return featureMap[projectType] || featureMap.defi;
  }

  private async analyzePartnershipEcosystem(project: any, competitors: any[]): Promise<any> {
    return {
      integration_partners: ['Protocol A', 'Protocol B', 'Infrastructure C'],
      strategic_alliances: ['Partnership 1', 'Partnership 2'],
      competitor_partnerships: competitors.reduce((acc, comp) => {
        acc[comp.name] = ['Partner X', 'Partner Y'];
        return acc;
      }, {} as Record<string, string[]>),
      partnership_opportunities: [
        'Cross-chain bridge integration',
        'Yield aggregator partnerships',
        'Wallet integrations',
      ],
    };
  }

  private async assessMarketTrends(projectType: string): Promise<any> {
    const trendMap: Record<string, any> = {
      defi: {
        sector_growth_rate: 45,
        emerging_trends: ['Layer 2 adoption', 'Real-world assets', 'Cross-chain DeFi'],
        technology_shifts: ['Account abstraction', 'Intent-based architecture'],
        regulatory_impact: 'Increasing regulatory clarity driving institutional adoption',
        market_consolidation: false,
      },
      nft: {
        sector_growth_rate: 15,
        emerging_trends: ['Utility NFTs', 'Gaming integration', 'Dynamic NFTs'],
        technology_shifts: ['ERC-6551', 'Compressed NFTs'],
        regulatory_impact: 'IP and creator rights becoming more defined',
        market_consolidation: true,
      },
    };

    return trendMap[projectType] || trendMap.defi;
  }

  private async identifyCompetitiveThreats(project: any, competitors: any[]): Promise<any[]> {
    const threats = [
      {
        threat: 'Market leader expanding feature set',
        probability: 'high' as const,
        impact: 'high' as const,
        mitigation_strategy: 'Focus on innovation and niche differentiation',
      },
      {
        threat: 'New well-funded competitor entering market',
        probability: 'medium' as const,
        impact: 'medium' as const,
        mitigation_strategy: 'Build strong community and partnerships',
      },
      {
        threat: 'Regulatory changes affecting DeFi',
        probability: 'medium' as const,
        impact: 'high' as const,
        mitigation_strategy: 'Ensure compliance and work with regulators',
      },
    ];

    return threats;
  }

  private async identifyOpportunities(project: any, marketTrends: any, featureComparison: any): Promise<any[]> {
    const opportunities = [
      {
        opportunity: 'Underserved market segment',
        market_size: 500000000,
        timeline: '6-12 months',
        requirements: ['Product development', 'Go-to-market strategy'],
      },
      {
        opportunity: 'Cross-chain expansion',
        market_size: 200000000,
        timeline: '3-6 months',
        requirements: ['Technical integration', 'Liquidity partnerships'],
      },
    ];

    // Add opportunities based on feature gaps
    for (const innovation of featureComparison.innovation_opportunities) {
      opportunities.push({
        opportunity: `Market leadership in ${innovation}`,
        market_size: 100000, // Default market size estimate
        timeline: '2-4 months',
        requirements: ['Feature enhancement', 'Marketing push'],
      });
    }

    return opportunities;
  }

  private async synthesizeAnalysis(data: any): Promise<any> {
    const prompt = `
Analyze the following competitive landscape data and provide comprehensive insights:

${JSON.stringify(data, null, 2)}

Provide analysis covering:
1. Competitive positioning and market dynamics
2. Key differentiators and competitive advantages
3. Feature gaps and innovation opportunities
4. Partnership strategy implications
5. Market trends impact on competitive landscape
6. Strategic recommendations for market positioning
`;

    return await this.generateStructuredResponse(prompt, CompetitorAnalysisSchema, data);
  }

  private async generateSummary(analysis: any): Promise<string> {
    const prompt = `
Create a concise executive summary of the competitive analysis:

${JSON.stringify(analysis, null, 2)}

Focus on:
- Competitive positioning in the market
- Key competitive advantages and disadvantages
- Major threats and opportunities
- Strategic implications for growth
- Recommended competitive strategies
`;

    return await this.generateTextResponse(prompt, analysis);
  }

  private async generateRecommendations(analysis: any): Promise<string[]> {
    const recommendations = [];

    if (analysis.market_positioning?.quadrant === 'niche') {
      recommendations.push('Focus on niche market dominance before expanding');
    }

    if (analysis.feature_comparison?.feature_gap_analysis?.length > 0) {
      recommendations.push('Address critical feature gaps to remain competitive');
    }

    if (analysis.opportunities?.length > 2) {
      recommendations.push('Prioritize high-impact opportunities for market expansion');
    }

    if (analysis.competitive_threats?.some((t: any) => t.probability === 'high')) {
      recommendations.push('Develop proactive strategies for high-probability threats');
    }

    return recommendations;
  }

  private extractMarketAnalysis(analysis: any): any {
    return {
      market_position: analysis.market_positioning,
      competitive_landscape: {
        direct_competitors: analysis.direct_competitors?.length || 0,
        indirect_competitors: analysis.indirect_competitors?.length || 0,
        market_concentration: this.calculateMarketConcentration(analysis.direct_competitors),
      },
      growth_opportunities: analysis.opportunities?.length || 0,
      threat_level: this.calculateOverallThreatLevel(analysis.competitive_threats),
    };
  }

  private calculateMarketConcentration(competitors: any[]): string {
    if (!competitors) return 'unknown';
    const topThreeShare = competitors
      .sort((a, b) => b.market_share - a.market_share)
      .slice(0, 3)
      .reduce((sum, comp) => sum + comp.market_share, 0);
    
    if (topThreeShare > 70) return 'high';
    if (topThreeShare > 50) return 'medium';
    return 'low';
  }

  private calculateOverallThreatLevel(threats: any[]): string {
    if (!threats) return 'low';
    const highThreats = threats.filter(t => t.probability === 'high' && t.impact === 'high');
    if (highThreats.length > 0) return 'high';
    const mediumThreats = threats.filter(t => 
      (t.probability === 'high' && t.impact === 'medium') ||
      (t.probability === 'medium' && t.impact === 'high')
    );
    if (mediumThreats.length > 1) return 'medium';
    return 'low';
  }

  private generateSources(directCompetitors: any[], indirectCompetitors: any[]): string[] {
    const sources = [
      'DeFiLlama - Protocol TVL and market data',
      'CoinGecko - Market cap and token metrics',
      'Public documentation and websites',
      'GitHub repositories for open source analysis',
    ];

    if (directCompetitors.length > 0) {
      sources.push(`Direct competitor analysis: ${directCompetitors.map(c => c.name).join(', ')}`);
    }

    if (indirectCompetitors.length > 0) {
      sources.push(`Indirect competitor monitoring: ${indirectCompetitors.map(c => c.name).join(', ')}`);
    }

    return sources;
  }
}