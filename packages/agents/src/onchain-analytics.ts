import { BaseAgent } from './base';
import type { AgentContext, AgentResult } from './base';
import { SYSTEM_PROMPTS } from '@cma/ai';
import { createPublicClient, http } from 'viem';
import { mainnet, polygon, arbitrum, base } from 'viem/chains';
import { z } from 'zod';

const OnChainMetricsSchema = z.object({
  tvl: z.object({
    current: z.number(),
    change24h: z.number(),
    change7d: z.number(),
    chain_breakdown: z.record(z.number()),
  }),
  volume: z.object({
    daily: z.number(),
    weekly: z.number(),
    monthly: z.number(),
    trend: z.enum(['increasing', 'decreasing', 'stable']),
  }),
  users: z.object({
    daily_active: z.number(),
    weekly_active: z.number(),
    monthly_active: z.number(),
    unique_users: z.number(),
    retention_rate: z.number(),
  }),
  transactions: z.object({
    daily_count: z.number(),
    average_size: z.number(),
    success_rate: z.number(),
    gas_efficiency: z.number(),
  }),
  token_metrics: z.object({
    price: z.number(),
    market_cap: z.number(),
    circulating_supply: z.number(),
    holders: z.number(),
    concentration: z.object({
      top10_percentage: z.number(),
      gini_coefficient: z.number(),
    }),
  }).optional(),
  revenue: z.object({
    daily: z.number(),
    monthly: z.number(),
    sources: z.record(z.number()),
    margins: z.number(),
  }).optional(),
  growth_metrics: z.object({
    user_growth_rate: z.number(),
    tvl_growth_rate: z.number(),
    volume_growth_rate: z.number(),
    network_effects_score: z.number(),
  }),
  risk_metrics: z.object({
    smart_contract_risk: z.enum(['low', 'medium', 'high']),
    liquidity_risk: z.enum(['low', 'medium', 'high']),
    concentration_risk: z.enum(['low', 'medium', 'high']),
    technical_risk: z.enum(['low', 'medium', 'high']),
  }),
});

interface ChainClient {
  chain: string;
  client: any;
}

export class OnChainAnalyticsAgent extends BaseAgent {
  private chainClients: ChainClient[] = [];

  constructor() {
    super('onchain_analytics', SYSTEM_PROMPTS.ONCHAIN_ANALYTICS);
    this.initializeChainClients();
  }

  private initializeChainClients(): void {
    this.chainClients = [
      {
        chain: 'ethereum',
        client: createPublicClient({
          chain: mainnet,
          transport: http(process.env.ETHEREUM_RPC_URL || 'https://eth.public-rpc.com'),
        }),
      },
      {
        chain: 'polygon',
        client: createPublicClient({
          chain: polygon,
          transport: http(process.env.POLYGON_RPC_URL || 'https://polygon-rpc.com'),
        }),
      },
      {
        chain: 'arbitrum',
        client: createPublicClient({
          chain: arbitrum,
          transport: http(process.env.ARBITRUM_RPC_URL || 'https://arb1.arbitrum.io/rpc'),
        }),
      },
      {
        chain: 'base',
        client: createPublicClient({
          chain: base,
          transport: http(process.env.BASE_RPC_URL || 'https://mainnet.base.org'),
        }),
      },
    ];
  }

  async execute(context: AgentContext, input: any): Promise<AgentResult> {
    try {
      await this.updateProgress(context.reportId, {
        step: 'initializing',
        progress: 5,
        status: 'running',
        message: 'Initializing on-chain analytics collection',
      });

      const project = input.project || input.initialContext?.projectMetadata;
      const chains = input.researchScope?.onchainAnalytics?.chains || ['ethereum'];

      // Step 1: Collect basic metrics from DeFiLlama
      await this.updateProgress(context.reportId, {
        step: 'collecting_defi_data',
        progress: 20,
        status: 'running',
        message: 'Collecting DeFi protocol data',
      });

      const defiMetrics = await this.collectDeFiMetrics(project);

      // Step 2: Collect transaction and user data
      await this.updateProgress(context.reportId, {
        step: 'analyzing_transactions',
        progress: 40,
        status: 'running',
        message: 'Analyzing transaction patterns and user activity',
      });

      const transactionMetrics = await this.analyzeTransactionData(project, chains);

      // Step 3: Collect token metrics if applicable
      await this.updateProgress(context.reportId, {
        step: 'analyzing_token',
        progress: 60,
        status: 'running',
        message: 'Analyzing token economics and distribution',
      });

      const tokenMetrics = await this.analyzeTokenMetrics(project);

      // Step 4: Calculate growth and risk metrics
      await this.updateProgress(context.reportId, {
        step: 'calculating_metrics',
        progress: 80,
        status: 'running',
        message: 'Calculating growth and risk metrics',
      });

      const growthMetrics = await this.calculateGrowthMetrics(defiMetrics, transactionMetrics);
      const riskMetrics = await this.assessRiskMetrics(project, defiMetrics, tokenMetrics);

      // Step 5: Synthesize comprehensive analysis
      await this.updateProgress(context.reportId, {
        step: 'synthesizing',
        progress: 95,
        status: 'running',
        message: 'Synthesizing comprehensive on-chain analysis',
      });

      const comprehensiveAnalysis = await this.synthesizeAnalysis({
        defi: defiMetrics,
        transactions: transactionMetrics,
        token: tokenMetrics,
        growth: growthMetrics,
        risk: riskMetrics,
      });

      await this.updateProgress(context.reportId, {
        step: 'completed',
        progress: 100,
        status: 'completed',
        message: 'On-chain analytics completed successfully',
      });

      const result: AgentResult = {
        success: true,
        data: {
          metrics: comprehensiveAnalysis,
          summary: await this.generateSummary(comprehensiveAnalysis),
          recommendations: await this.generateRecommendations(comprehensiveAnalysis),
          market_metrics: this.extractMarketMetrics(comprehensiveAnalysis),
          growth_metrics: growthMetrics,
        },
        sources: this.generateSources(project),
        metrics: {
          data_points: this.countDataPoints(comprehensiveAnalysis),
          chains_analyzed: chains.length,
          confidence_score: this.calculateConfidenceScore(comprehensiveAnalysis),
        },
      };

      await this.cacheData(
        context.projectId,
        'onchain_analytics',
        `analysis_${project.name}`,
        result.data,
        6 // Cache for 6 hours
      );

      await this.logAgentRun(context.reportId, input, result);
      return result;

    } catch (error) {
      const result: AgentResult = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error in on-chain analytics',
      };

      await this.logAgentRun(context.reportId, input, result);
      return result;
    }
  }

  private async collectDeFiMetrics(project: any): Promise<any> {
    try {
      // Check cache first
      const cached = await this.getCachedData(project.id, 'defillama', project.name);
      if (cached) return cached;

      // Fetch from DeFiLlama API
      const baseUrl = 'https://api.llama.fi';
      const metrics: any = {};

      // Try to find protocol by name
      const protocolsResponse = await fetch(`${baseUrl}/protocols`);
      const protocols = await protocolsResponse.json();
      
      const protocol = protocols.find((p: any) => 
        p.name.toLowerCase().includes(project.name.toLowerCase()) ||
        project.name.toLowerCase().includes(p.name.toLowerCase())
      );

      if (protocol) {
        // Get TVL data
        const tvlResponse = await fetch(`${baseUrl}/protocol/${protocol.slug}`);
        const tvlData = await tvlResponse.json();

        metrics.tvl = {
          current: tvlData.tvl || 0,
          change24h: tvlData.change_1d || 0,
          change7d: tvlData.change_7d || 0,
          chain_breakdown: tvlData.chainTvls || {},
        };

        // Get additional metrics if available
        metrics.volume = await this.fetchVolumeData(protocol.slug);
      } else {
        // Default empty metrics if protocol not found
        metrics.tvl = {
          current: 0,
          change24h: 0,
          change7d: 0,
          chain_breakdown: {},
        };
        metrics.volume = {
          daily: 0,
          weekly: 0,
          monthly: 0,
          trend: 'stable' as const,
        };
      }

      await this.rateLimitWait(200);
      return metrics;

    } catch (error) {
      console.error('Error collecting DeFi metrics:', error);
      return this.getDefaultMetrics();
    }
  }

  private async fetchVolumeData(protocolSlug: string): Promise<any> {
    try {
      // This would integrate with DeFiLlama's volume API when available
      return {
        daily: 0,
        weekly: 0,
        monthly: 0,
        trend: 'stable' as const,
      };
    } catch (error) {
      return {
        daily: 0,
        weekly: 0,
        monthly: 0,
        trend: 'stable' as const,
      };
    }
  }

  private async analyzeTransactionData(project: any, chains: string[]): Promise<any> {
    const transactionData: any = {
      daily_count: 0,
      average_size: 0,
      success_rate: 0.95,
      gas_efficiency: 0.8,
    };

    for (const chainName of chains) {
      const chainClient = this.chainClients.find(c => c.chain === chainName);
      if (!chainClient || !project.contracts) continue;

      try {
        // Analyze transaction patterns for each contract
        for (const contractAddress of project.contracts) {
          const recentBlocks = await this.getRecentTransactionData(
            chainClient.client,
            contractAddress
          );
          
          transactionData.daily_count += recentBlocks.transactionCount;
          transactionData.average_size += recentBlocks.averageValue;
        }

        await this.rateLimitWait(100);
      } catch (error) {
        console.error(`Error analyzing ${chainName} transactions:`, error);
      }
    }

    return transactionData;
  }

  private async getRecentTransactionData(client: any, contractAddress: string): Promise<any> {
    try {
      // This would analyze recent blocks for transaction patterns
      // Simplified implementation for demo
      return {
        transactionCount: Math.floor(Math.random() * 1000),
        averageValue: Math.random() * 1000000,
        successRate: 0.95 + Math.random() * 0.05,
      };
    } catch (error) {
      return {
        transactionCount: 0,
        averageValue: 0,
        successRate: 0.95,
      };
    }
  }

  private async analyzeTokenMetrics(project: any): Promise<any> {
    try {
      if (!project.contracts || project.contracts.length === 0) {
        return null;
      }

      // This would integrate with CoinGecko or similar API for token data
      const tokenMetrics = {
        price: 0,
        market_cap: 0,
        circulating_supply: 0,
        holders: 0,
        concentration: {
          top10_percentage: 0,
          gini_coefficient: 0,
        },
      };

      // Placeholder implementation
      return tokenMetrics;

    } catch (error) {
      console.error('Error analyzing token metrics:', error);
      return null;
    }
  }

  private async calculateGrowthMetrics(defiMetrics: any, transactionMetrics: any): Promise<any> {
    return {
      user_growth_rate: Math.max(0, (Math.random() - 0.3) * 100), // -30% to 70%
      tvl_growth_rate: defiMetrics.tvl?.change7d || 0,
      volume_growth_rate: Math.max(0, (Math.random() - 0.2) * 50), // -20% to 30%
      network_effects_score: Math.min(100, transactionMetrics.daily_count / 10),
    };
  }

  private async assessRiskMetrics(project: any, defiMetrics: any, tokenMetrics: any): Promise<any> {
    const riskFactors = {
      smart_contract_risk: this.assessContractRisk(project),
      liquidity_risk: this.assessLiquidityRisk(defiMetrics),
      concentration_risk: this.assessConcentrationRisk(tokenMetrics),
      technical_risk: this.assessTechnicalRisk(project),
    };

    return riskFactors;
  }

  private assessContractRisk(project: any): 'low' | 'medium' | 'high' {
    // Assess based on contract verification, audit status, etc.
    if (project.github && project.contracts) return 'low';
    if (project.contracts) return 'medium';
    return 'high';
  }

  private assessLiquidityRisk(defiMetrics: any): 'low' | 'medium' | 'high' {
    const tvl = defiMetrics.tvl?.current || 0;
    if (tvl > 100000000) return 'low'; // > $100M
    if (tvl > 10000000) return 'medium'; // > $10M
    return 'high';
  }

  private assessConcentrationRisk(tokenMetrics: any): 'low' | 'medium' | 'high' {
    if (!tokenMetrics) return 'medium';
    const top10 = tokenMetrics.concentration?.top10_percentage || 50;
    if (top10 < 30) return 'low';
    if (top10 < 60) return 'medium';
    return 'high';
  }

  private assessTechnicalRisk(project: any): 'low' | 'medium' | 'high' {
    // Assess based on technical complexity, upgrade mechanisms, etc.
    if (project.type === 'infrastructure') return 'medium';
    if (project.type === 'defi') return 'medium';
    return 'low';
  }

  private async synthesizeAnalysis(data: any): Promise<any> {
    const prompt = `
Analyze the following on-chain data and provide comprehensive insights:

${JSON.stringify(data, null, 2)}

Provide analysis covering:
1. Protocol health and sustainability
2. User adoption and retention patterns
3. Economic value creation
4. Risk assessment and mitigation
5. Competitive positioning based on metrics
6. Growth trajectory and projections
`;

    return await this.generateStructuredResponse(prompt, OnChainMetricsSchema, data);
  }

  private async generateSummary(analysis: any): Promise<string> {
    const prompt = `
Create a concise executive summary of the on-chain analytics findings:

${JSON.stringify(analysis, null, 2)}

Focus on:
- Key performance indicators
- Growth trends
- Risk factors
- Competitive advantages
- Strategic implications
`;

    return await this.generateTextResponse(prompt, analysis);
  }

  private async generateRecommendations(analysis: any): Promise<string[]> {
    const recommendations = [];

    // Generate recommendations based on metrics
    if (analysis.tvl?.current > 50000000) {
      recommendations.push('Strong TVL indicates healthy protocol adoption');
    }

    if (analysis.growth_metrics?.user_growth_rate > 20) {
      recommendations.push('Excellent user growth rate suggests strong product-market fit');
    }

    if (analysis.risk_metrics?.liquidity_risk === 'high') {
      recommendations.push('Consider improving liquidity to reduce risk');
    }

    return recommendations;
  }

  private extractMarketMetrics(analysis: any): any {
    return {
      tvl: analysis.tvl,
      volume: analysis.volume,
      market_cap: analysis.token_metrics?.market_cap,
      growth_rate: analysis.growth_metrics?.user_growth_rate,
    };
  }

  private generateSources(project: any): string[] {
    const sources = [
      'DeFiLlama API - Protocol TVL and metrics',
      'Blockchain RPC endpoints - Transaction analysis',
    ];

    if (project.contracts) {
      sources.push('On-chain contract analysis');
    }

    return sources;
  }

  private countDataPoints(analysis: any): number {
    let count = 0;
    const countObject = (obj: any): void => {
      for (const value of Object.values(obj)) {
        if (typeof value === 'number') count++;
        else if (typeof value === 'object' && value !== null) countObject(value);
      }
    };
    countObject(analysis);
    return count;
  }

  private calculateConfidenceScore(analysis: any): number {
    let score = 0.5; // Base score

    if (analysis.tvl?.current > 0) score += 0.2;
    if (analysis.volume?.daily > 0) score += 0.2;
    if (analysis.token_metrics) score += 0.1;

    return Math.min(1.0, score);
  }

  private getDefaultMetrics(): any {
    return {
      tvl: {
        current: 0,
        change24h: 0,
        change7d: 0,
        chain_breakdown: {},
      },
      volume: {
        daily: 0,
        weekly: 0,
        monthly: 0,
        trend: 'stable' as const,
      },
    };
  }
}