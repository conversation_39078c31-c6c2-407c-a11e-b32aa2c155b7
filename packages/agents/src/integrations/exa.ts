import Exa from 'exa-js';

export class ExaService {
  private client: Exa;

  constructor() {
    this.client = new Exa(process.env.EXA_API_KEY!);
  }

  async searchWeb(query: string, options?: any): Promise<any> {
    try {
      const results = await this.client.searchAndContents(query, {
        type: 'neural',
        useAutoprompt: true,
        numResults: options?.numResults || 10,
        text: {
          maxCharacters: options?.maxCharacters || 2000,
          includeHtmlTags: false,
        },
        highlights: {
          numSentences: 3,
          highlightsPerUrl: 2,
        },
        ...options,
      });

      return {
        success: true,
        results: results.results.map((result: any) => ({
          title: result.title,
          url: result.url,
          content: result.text,
          highlights: result.highlights,
          score: result.score,
          publishedDate: result.publishedDate,
        })),
        autopromptString: results.autopromptString,
      };
    } catch (error) {
      console.error('Exa search error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  async findSimilarContent(url: string, options?: any): Promise<any> {
    try {
      const results = await this.client.findSimilarAndContents(url, {
        numResults: options?.numResults || 5,
        text: {
          maxCharacters: options?.maxCharacters || 1500,
        },
        ...options,
      });

      return {
        success: true,
        results: results.results.map((result: any) => ({
          title: result.title,
          url: result.url,
          content: result.text,
          score: result.score,
          publishedDate: result.publishedDate,
        })),
      };
    } catch (error) {
      console.error('Exa similar content error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  async researchProject(projectName: string, projectType: string): Promise<any> {
    const queries = [
      `${projectName} ${projectType} protocol analysis`,
      `${projectName} documentation technical overview`,
      `${projectName} tokenomics and economics`,
      `${projectName} competitors and market analysis`,
      `${projectName} news and updates 2024`,
    ];

    const allResults = [];

    for (const query of queries) {
      const result = await this.searchWeb(query, {
        numResults: 5,
        maxCharacters: 1500,
      });

      if (result.success) {
        allResults.push({
          query,
          results: result.results,
        });
      }

      // Rate limiting
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    return {
      success: true,
      research: allResults,
      totalResults: allResults.reduce((sum, r) => sum + r.results.length, 0),
    };
  }

  async findCompetitorInformation(projectName: string, competitors: string[]): Promise<any> {
    const competitorData = [];

    for (const competitor of competitors) {
      const queries = [
        `${competitor} vs ${projectName} comparison`,
        `${competitor} features and capabilities`,
        `${competitor} market position analysis`,
      ];

      const competitorResults = [];

      for (const query of queries) {
        const result = await this.searchWeb(query, {
          numResults: 3,
          maxCharacters: 1000,
        });

        if (result.success) {
          competitorResults.push(...result.results);
        }

        await new Promise(resolve => setTimeout(resolve, 300));
      }

      competitorData.push({
        competitor,
        results: competitorResults,
      });
    }

    return {
      success: true,
      competitorData,
    };
  }

  async findTechnicalDocumentation(projectName: string, githubUrl?: string): Promise<any> {
    const queries = [
      `${projectName} technical documentation`,
      `${projectName} developer docs API`,
      `${projectName} smart contract documentation`,
      `${projectName} architecture overview`,
    ];

    if (githubUrl) {
      queries.push(`site:github.com ${projectName} README`);
    }

    const technicalResults = [];

    for (const query of queries) {
      const result = await this.searchWeb(query, {
        numResults: 3,
        maxCharacters: 2000,
        includeDomains: githubUrl ? ['github.com', 'docs.' + projectName.toLowerCase() + '.com'] : undefined,
      });

      if (result.success) {
        technicalResults.push({
          query,
          results: result.results,
        });
      }

      await new Promise(resolve => setTimeout(resolve, 400));
    }

    return {
      success: true,
      technicalDocs: technicalResults,
    };
  }

  async findMarketInsights(projectType: string, keywords: string[]): Promise<any> {
    const marketQueries = [
      `${projectType} market analysis 2024`,
      `${projectType} trends and growth`,
      `${projectType} industry report`,
      ...keywords.map(keyword => `${keyword} ${projectType} market`),
    ];

    const marketResults = [];

    for (const query of marketQueries) {
      const result = await this.searchWeb(query, {
        numResults: 4,
        maxCharacters: 1500,
        category: 'company',
      });

      if (result.success) {
        marketResults.push({
          query,
          results: result.results,
        });
      }

      await new Promise(resolve => setTimeout(resolve, 600));
    }

    return {
      success: true,
      marketInsights: marketResults,
    };
  }

  async searchNews(projectName: string, timeframe: string = '2024'): Promise<any> {
    const query = `${projectName} news updates ${timeframe}`;
    
    const result = await this.searchWeb(query, {
      numResults: 10,
      maxCharacters: 1000,
      startCrawlDate: '2024-01-01',
    });

    if (result.success) {
      return {
        success: true,
        news: result.results.filter((item: any) => 
          item.publishedDate && item.publishedDate.includes(timeframe)
        ),
      };
    }

    return result;
  }
}