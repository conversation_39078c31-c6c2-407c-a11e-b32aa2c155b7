export class BlockchainDataService {
  private readonly DEFILLAMA_BASE = 'https://api.llama.fi';
  private readonly COINGECKO_BASE = 'https://api.coingecko.com/api/v3';

  async getDeFiLlamaProtocol(protocolSlug: string): Promise<any> {
    try {
      const response = await fetch(`${this.DEFILLAMA_BASE}/protocol/${protocolSlug}`);
      const data = await response.json();
      
      return {
        success: true,
        data: {
          tvl: data.tvl || 0,
          change_1d: data.change_1d || 0,
          change_7d: data.change_7d || 0,
          change_1m: data.change_1m || 0,
          chainTvls: data.chainTvls || {},
          tokensInUsd: data.tokensInUsd || [],
          tokens: data.tokens || [],
          methodology: data.methodology || '',
        }
      };
    } catch (error) {
      console.error('DeFiLlama API error:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  async searchDeFiLlamaProtocols(query: string): Promise<any> {
    try {
      const response = await fetch(`${this.DEFILLAMA_BASE}/protocols`);
      const protocols = await response.json();
      
      const matches = protocols.filter((p: any) => 
        p.name.toLowerCase().includes(query.toLowerCase()) ||
        p.symbol?.toLowerCase().includes(query.toLowerCase()) ||
        p.description?.toLowerCase().includes(query.toLowerCase())
      );

      return {
        success: true,
        protocols: matches.slice(0, 10), // Limit to top 10 matches
      };
    } catch (error) {
      console.error('DeFiLlama search error:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  async getTokenPrice(tokenId: string): Promise<any> {
    try {
      const response = await fetch(
        `${this.COINGECKO_BASE}/simple/price?ids=${tokenId}&vs_currencies=usd&include_24hr_change=true&include_market_cap=true&include_24hr_vol=true`
      );
      const data = await response.json();
      
      if (data[tokenId]) {
        return {
          success: true,
          data: {
            price: data[tokenId].usd,
            change_24h: data[tokenId].usd_24h_change,
            market_cap: data[tokenId].usd_market_cap,
            volume_24h: data[tokenId].usd_24h_vol,
          }
        };
      }
      
      return { success: false, error: 'Token not found' };
    } catch (error) {
      console.error('CoinGecko API error:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  async getTokenInfo(tokenId: string): Promise<any> {
    try {
      const response = await fetch(`${this.COINGECKO_BASE}/coins/${tokenId}`);
      const data = await response.json();
      
      return {
        success: true,
        data: {
          name: data.name,
          symbol: data.symbol,
          description: data.description?.en || '',
          homepage: data.links?.homepage?.[0] || '',
          blockchain_site: data.links?.blockchain_site || [],
          official_forum_url: data.links?.official_forum_url || [],
          chat_url: data.links?.chat_url || [],
          announcement_url: data.links?.announcement_url || [],
          twitter_screen_name: data.links?.twitter_screen_name || '',
          facebook_username: data.links?.facebook_username || '',
          telegram_channel_identifier: data.links?.telegram_channel_identifier || '',
          subreddit_url: data.links?.subreddit_url || '',
          repos_url: data.links?.repos_url || {},
          market_cap_rank: data.market_cap_rank,
          coingecko_rank: data.coingecko_rank,
          coingecko_score: data.coingecko_score,
          developer_score: data.developer_score,
          community_score: data.community_score,
          liquidity_score: data.liquidity_score,
          public_interest_score: data.public_interest_score,
        }
      };
    } catch (error) {
      console.error('CoinGecko token info error:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  async getTVLCharts(protocolSlug: string): Promise<any> {
    try {
      const response = await fetch(`${this.DEFILLAMA_BASE}/protocol/${protocolSlug}`);
      const data = await response.json();
      
      return {
        success: true,
        tvl: data.tvl || [],
        tokensInUsd: data.tokensInUsd || [],
        tokens: data.tokens || [],
      };
    } catch (error) {
      console.error('TVL charts error:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  async getChainTVL(chain: string): Promise<any> {
    try {
      const response = await fetch(`${this.DEFILLAMA_BASE}/v2/chains`);
      const chains = await response.json();
      
      const chainData = chains.find((c: any) => 
        c.name.toLowerCase() === chain.toLowerCase()
      );
      
      if (chainData) {
        return {
          success: true,
          data: {
            tvl: chainData.tvl,
            tokenSymbol: chainData.tokenSymbol,
            cmcId: chainData.cmcId,
            gecko_id: chainData.gecko_id,
          }
        };
      }
      
      return { success: false, error: 'Chain not found' };
    } catch (error) {
      console.error('Chain TVL error:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  async getProtocolYields(protocolSlug: string): Promise<any> {
    try {
      // DeFiLlama yields API
      const response = await fetch(`${this.DEFILLAMA_BASE}/yields/pools`);
      const pools = await response.json();
      
      const protocolPools = pools.data?.filter((pool: any) => 
        pool.project?.toLowerCase().includes(protocolSlug.toLowerCase())
      ) || [];
      
      return {
        success: true,
        pools: protocolPools.slice(0, 20), // Limit results
      };
    } catch (error) {
      console.error('Protocol yields error:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  async getFeesAndRevenue(protocolSlug: string): Promise<any> {
    try {
      const response = await fetch(`${this.DEFILLAMA_BASE}/fees/${protocolSlug}`);
      const data = await response.json();
      
      return {
        success: true,
        data: {
          totalDataChart: data.totalDataChart || [],
          totalDataChartBreakdown: data.totalDataChartBreakdown || [],
          total24h: data.total24h || 0,
          total7d: data.total7d || 0,
          total30d: data.total30d || 0,
          revenue24h: data.revenue24h || 0,
          revenue7d: data.revenue7d || 0,
          revenue30d: data.revenue30d || 0,
        }
      };
    } catch (error) {
      console.error('Fees and revenue error:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  async getVolume(protocolSlug: string): Promise<any> {
    try {
      const response = await fetch(`${this.DEFILLAMA_BASE}/summary/dexs/${protocolSlug}`);
      const data = await response.json();
      
      return {
        success: true,
        data: {
          total24h: data.total24h || 0,
          total7d: data.total7d || 0,
          change_1d: data.change_1d || 0,
          change_7d: data.change_7d || 0,
          change_1m: data.change_1m || 0,
        }
      };
    } catch (error) {
      console.error('Volume error:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  async searchTokens(query: string): Promise<any> {
    try {
      const response = await fetch(`${this.COINGECKO_BASE}/search?query=${encodeURIComponent(query)}`);
      const data = await response.json();
      
      return {
        success: true,
        coins: data.coins?.slice(0, 10) || [],
        exchanges: data.exchanges?.slice(0, 5) || [],
        categories: data.categories?.slice(0, 5) || [],
      };
    } catch (error) {
      console.error('Token search error:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  async rateLimitedFetch(url: string, delay: number = 1000): Promise<any> {
    await new Promise(resolve => setTimeout(resolve, delay));
    return fetch(url);
  }
}