import FirecrawlApp from '@mendable/firecrawl-js';

export class FirecrawlService {
  private client: FirecrawlApp;

  constructor() {
    this.client = new FirecrawlApp({
      apiKey: process.env.FIRECRAWL_API_KEY!,
    });
  }

  async scrapeUrl(url: string, options?: any): Promise<any> {
    try {
      const result = await this.client.scrapeUrl(url, {
        formats: ['markdown', 'html'],
        includeTags: ['title', 'meta', 'h1', 'h2', 'h3', 'p', 'a'],
        excludeTags: ['script', 'style', 'nav', 'footer'],
        waitFor: 3000,
        ...options,
      });

      if ('data' in result) {
        return {
          success: true,
          data: result.data,
          metadata: 'metadata' in result ? result.metadata : {},
        };
      } else {
        return {
          success: false,
          error: 'Scraping failed',
        };
      }
    } catch (error) {
      console.error('Firecrawl scraping error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  async crawlWebsite(url: string, options?: any): Promise<any> {
    try {
      const crawlResponse = await this.client.crawlUrl(url, {
        limit: options?.limit || 10,
        scrapeOptions: {
          formats: ['markdown'],
          includeTags: ['title', 'meta', 'h1', 'h2', 'h3', 'p'],
          excludeTags: ['script', 'style', 'nav', 'footer'],
        },
        ...options,
      });

      if (crawlResponse.success) {
        return {
          success: true,
          jobId: (crawlResponse as any).jobId,
        };
      } else {
        return {
          success: false,
          error: 'Failed to start crawl job',
        };
      }
    } catch (error) {
      console.error('Firecrawl crawling error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  async getCrawlStatus(jobId: string): Promise<any> {
    try {
      const status = await this.client.checkCrawlStatus(jobId);
      return {
        success: true,
        status: (status as any).status,
        data: (status as any).data,
        completed: (status as any).status === 'completed',
      };
    } catch (error) {
      console.error('Firecrawl status check error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  async scrapeProjectWebsite(project: any): Promise<any> {
    if (!project.website) {
      return { success: false, error: 'No website provided' };
    }

    const result = await this.scrapeUrl(project.website, {
      includeTags: ['title', 'meta', 'h1', 'h2', 'h3', 'p', 'a', 'img'],
      excludeTags: ['script', 'style', 'nav', 'footer', 'aside'],
    });

    if (result.success) {
      return {
        success: true,
        content: result.data.markdown,
        metadata: {
          title: result.data.metadata?.title || '',
          description: result.data.metadata?.description || '',
          keywords: result.data.metadata?.keywords || '',
          url: project.website,
        },
      };
    }

    return result;
  }

  async scrapeDocumentation(urls: string[]): Promise<any[]> {
    const results = [];
    
    for (const url of urls) {
      try {
        const result = await this.scrapeUrl(url, {
          includeTags: ['h1', 'h2', 'h3', 'h4', 'p', 'code', 'pre', 'li'],
          excludeTags: ['script', 'style', 'nav', 'footer', 'header'],
          waitFor: 2000,
        });

        if (result.success) {
          results.push({
            url,
            content: result.data.markdown,
            title: result.data.metadata?.title || '',
            success: true,
          });
        } else {
          results.push({
            url,
            success: false,
            error: result.error,
          });
        }

        // Rate limiting
        await new Promise(resolve => setTimeout(resolve, 1000));
      } catch (error) {
        results.push({
          url,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    }

    return results;
  }
}