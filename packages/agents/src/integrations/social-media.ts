export interface TwitterConfig {
  bearerToken?: string;
  apiKey?: string;
  apiSecret?: string;
  accessToken?: string;
  accessTokenSecret?: string;
}

export interface TelegramConfig {
  botToken?: string;
  channelId?: string;
}

export interface DiscordConfig {
  botToken?: string;
  clientId?: string;
}

export class SocialMediaService {
  private twitterConfig: TwitterConfig;
  private telegramConfig: TelegramConfig;
  private discordConfig: DiscordConfig;

  constructor() {
    this.twitterConfig = {
      bearerToken: process.env.TWITTER_BEARER_TOKEN,
      apiKey: process.env.TWITTER_API_KEY,
      apiSecret: process.env.TWITTER_API_SECRET,
      accessToken: process.env.TWITTER_ACCESS_TOKEN,
      accessTokenSecret: process.env.TWITTER_ACCESS_TOKEN_SECRET,
    };
    
    this.telegramConfig = {
      botToken: process.env.TELEGRAM_BOT_TOKEN,
      channelId: process.env.TELEGRAM_CHANNEL_ID,
    };
    
    this.discordConfig = {
      botToken: process.env.DISCORD_BOT_TOKEN,
      clientId: process.env.DISCORD_CLIENT_ID,
    };
  }

  async getTwitterProfile(username: string): Promise<any> {
    try {
      if (!this.twitterConfig.bearerToken) {
        return this.fallbackTwitterAnalysis(username);
      }

      const response = await fetch(
        `https://api.twitter.com/2/users/by/username/${username}?user.fields=created_at,description,entities,id,location,name,pinned_tweet_id,profile_image_url,protected,public_metrics,url,username,verified,verified_type`,
        {
          headers: {
            'Authorization': `Bearer ${this.twitterConfig.bearerToken}`,
          },
        }
      );

      if (!response.ok) {
        console.warn('Twitter API failed, using fallback');
        return this.fallbackTwitterAnalysis(username);
      }

      const data = await response.json();
      
      return {
        success: true,
        data: {
          id: data.data?.id,
          username: data.data?.username,
          name: data.data?.name,
          description: data.data?.description || '',
          location: data.data?.location || '',
          created_at: data.data?.created_at,
          verified: data.data?.verified || false,
          followers_count: data.data?.public_metrics?.followers_count || 0,
          following_count: data.data?.public_metrics?.following_count || 0,
          tweet_count: data.data?.public_metrics?.tweet_count || 0,
          listed_count: data.data?.public_metrics?.listed_count || 0,
          profile_image_url: data.data?.profile_image_url || '',
          url: data.data?.url || '',
        },
      };
    } catch (error) {
      console.error('Twitter profile error:', error);
      return this.fallbackTwitterAnalysis(username);
    }
  }

  async getTwitterTweets(username: string, maxResults: number = 10): Promise<any> {
    try {
      if (!this.twitterConfig.bearerToken) {
        return this.fallbackTwitterTweets(username);
      }

      // First get user ID
      const userResponse = await fetch(
        `https://api.twitter.com/2/users/by/username/${username}`,
        {
          headers: {
            'Authorization': `Bearer ${this.twitterConfig.bearerToken}`,
          },
        }
      );

      if (!userResponse.ok) {
        return this.fallbackTwitterTweets(username);
      }

      const userData = await userResponse.json();
      const userId = userData.data?.id;

      if (!userId) {
        return this.fallbackTwitterTweets(username);
      }

      // Get user tweets
      const tweetsResponse = await fetch(
        `https://api.twitter.com/2/users/${userId}/tweets?max_results=${maxResults}&tweet.fields=created_at,author_id,context_annotations,conversation_id,entities,id,in_reply_to_user_id,lang,public_metrics,referenced_tweets,reply_settings,source,text`,
        {
          headers: {
            'Authorization': `Bearer ${this.twitterConfig.bearerToken}`,
          },
        }
      );

      if (!tweetsResponse.ok) {
        return this.fallbackTwitterTweets(username);
      }

      const tweetsData = await tweetsResponse.json();
      
      return {
        success: true,
        tweets: tweetsData.data?.map((tweet: any) => ({
          id: tweet.id,
          text: tweet.text,
          created_at: tweet.created_at,
          author_id: tweet.author_id,
          conversation_id: tweet.conversation_id,
          lang: tweet.lang,
          retweet_count: tweet.public_metrics?.retweet_count || 0,
          reply_count: tweet.public_metrics?.reply_count || 0,
          like_count: tweet.public_metrics?.like_count || 0,
          quote_count: tweet.public_metrics?.quote_count || 0,
          bookmark_count: tweet.public_metrics?.bookmark_count || 0,
          impression_count: tweet.public_metrics?.impression_count || 0,
        })) || [],
      };
    } catch (error) {
      console.error('Twitter tweets error:', error);
      return this.fallbackTwitterTweets(username);
    }
  }

  async searchTwitter(query: string, maxResults: number = 10): Promise<any> {
    try {
      if (!this.twitterConfig.bearerToken) {
        return this.fallbackTwitterSearch(query);
      }

      const response = await fetch(
        `https://api.twitter.com/2/tweets/search/recent?query=${encodeURIComponent(query)}&max_results=${maxResults}&tweet.fields=created_at,author_id,context_annotations,conversation_id,entities,id,in_reply_to_user_id,lang,public_metrics,referenced_tweets,reply_settings,source,text`,
        {
          headers: {
            'Authorization': `Bearer ${this.twitterConfig.bearerToken}`,
          },
        }
      );

      if (!response.ok) {
        return this.fallbackTwitterSearch(query);
      }

      const data = await response.json();
      
      return {
        success: true,
        tweets: data.data?.map((tweet: any) => ({
          id: tweet.id,
          text: tweet.text,
          created_at: tweet.created_at,
          author_id: tweet.author_id,
          conversation_id: tweet.conversation_id,
          lang: tweet.lang,
          retweet_count: tweet.public_metrics?.retweet_count || 0,
          reply_count: tweet.public_metrics?.reply_count || 0,
          like_count: tweet.public_metrics?.like_count || 0,
          quote_count: tweet.public_metrics?.quote_count || 0,
          bookmark_count: tweet.public_metrics?.bookmark_count || 0,
          impression_count: tweet.public_metrics?.impression_count || 0,
        })) || [],
        meta: data.meta,
      };
    } catch (error) {
      console.error('Twitter search error:', error);
      return this.fallbackTwitterSearch(query);
    }
  }

  async getTelegramChannelInfo(channelUsername: string): Promise<any> {
    try {
      if (!this.telegramConfig.botToken) {
        return this.fallbackTelegramAnalysis(channelUsername);
      }

      // Use Telegram Bot API to get chat info
      const response = await fetch(
        `https://api.telegram.org/bot${this.telegramConfig.botToken}/getChat?chat_id=@${channelUsername}`
      );

      if (!response.ok) {
        return this.fallbackTelegramAnalysis(channelUsername);
      }

      const data = await response.json();
      
      if (data.ok) {
        return {
          success: true,
          data: {
            id: data.result.id,
            title: data.result.title || '',
            username: data.result.username || '',
            description: data.result.description || '',
            invite_link: data.result.invite_link || '',
            pinned_message: data.result.pinned_message || null,
            photo: data.result.photo || null,
            member_count: data.result.member_count || 0,
          },
        };
      }

      return this.fallbackTelegramAnalysis(channelUsername);
    } catch (error) {
      console.error('Telegram channel error:', error);
      return this.fallbackTelegramAnalysis(channelUsername);
    }
  }

  async getDiscordServerInfo(serverId: string): Promise<any> {
    try {
      if (!this.discordConfig.botToken) {
        return this.fallbackDiscordAnalysis(serverId);
      }

      const response = await fetch(
        `https://discord.com/api/v10/guilds/${serverId}?with_counts=true`,
        {
          headers: {
            'Authorization': `Bot ${this.discordConfig.botToken}`,
          },
        }
      );

      if (!response.ok) {
        return this.fallbackDiscordAnalysis(serverId);
      }

      const data = await response.json();
      
      return {
        success: true,
        data: {
          id: data.id,
          name: data.name,
          description: data.description || '',
          icon: data.icon,
          banner: data.banner,
          owner_id: data.owner_id,
          member_count: data.approximate_member_count || 0,
          presence_count: data.approximate_presence_count || 0,
          features: data.features || [],
          verification_level: data.verification_level,
          default_message_notifications: data.default_message_notifications,
        },
      };
    } catch (error) {
      console.error('Discord server error:', error);
      return this.fallbackDiscordAnalysis(serverId);
    }
  }

  // Fallback methods for when APIs are unavailable
  private fallbackTwitterAnalysis(username: string): any {
    return {
      success: true,
      fallback: true,
      data: {
        username,
        name: username,
        description: `Analysis for @${username} - API unavailable`,
        followers_count: 0,
        following_count: 0,
        tweet_count: 0,
        verified: false,
        profile_image_url: '',
        url: `https://twitter.com/${username}`,
        estimated_engagement: 'low',
        activity_level: 'unknown',
      },
      limitations: 'Limited analysis due to API constraints',
    };
  }

  private fallbackTwitterTweets(username: string): any {
    return {
      success: true,
      fallback: true,
      tweets: [],
      message: `Unable to fetch tweets for @${username} - using alternative analysis methods`,
      limitations: 'Tweet content analysis unavailable',
    };
  }

  private fallbackTwitterSearch(query: string): any {
    return {
      success: true,
      fallback: true,
      tweets: [],
      message: `Unable to search Twitter for "${query}" - using alternative analysis methods`,
      limitations: 'Real-time Twitter search unavailable',
    };
  }

  private fallbackTelegramAnalysis(channelUsername: string): any {
    return {
      success: true,
      fallback: true,
      data: {
        username: channelUsername,
        title: channelUsername,
        description: `Analysis for ${channelUsername} - API unavailable`,
        member_count: 0,
        activity_level: 'unknown',
        estimated_growth: 'unknown',
      },
      limitations: 'Limited Telegram analysis due to API constraints',
    };
  }

  private fallbackDiscordAnalysis(serverId: string): any {
    return {
      success: true,
      fallback: true,
      data: {
        id: serverId,
        name: 'Discord Server',
        description: `Analysis for server ${serverId} - API unavailable`,
        member_count: 0,
        presence_count: 0,
        features: [],
        activity_level: 'unknown',
      },
      limitations: 'Limited Discord analysis due to API constraints',
    };
  }

  async analyzeSocialSentiment(projectName: string, handles: any): Promise<any> {
    const results: any = {
      twitter: null,
      telegram: null,
      discord: null,
      overall_sentiment: 'neutral',
      confidence: 0,
    };

    // Twitter analysis
    if (handles.twitter) {
      const twitterProfile = await this.getTwitterProfile(handles.twitter);
      const twitterTweets = await this.getTwitterTweets(handles.twitter, 20);
      const twitterSearch = await this.searchTwitter(projectName, 20);

      results.twitter = {
        profile: twitterProfile.data,
        recent_tweets: twitterTweets.tweets || [],
        mentions: twitterSearch.tweets || [],
        sentiment_analysis: this.analyzeTweetSentiment(twitterTweets.tweets || []),
        engagement_metrics: this.calculateTwitterEngagement(twitterProfile.data, twitterTweets.tweets || []),
        fallback_used: twitterProfile.fallback || false,
      };
    }

    // Telegram analysis
    if (handles.telegram) {
      const telegramInfo = await this.getTelegramChannelInfo(handles.telegram);
      results.telegram = {
        channel_info: telegramInfo.data,
        community_size: telegramInfo.data?.member_count || 0,
        activity_level: this.estimateActivityLevel(telegramInfo.data?.member_count || 0),
        fallback_used: telegramInfo.fallback || false,
      };
    }

    // Discord analysis
    if (handles.discord) {
      const discordInfo = await this.getDiscordServerInfo(handles.discord);
      results.discord = {
        server_info: discordInfo.data,
        member_count: discordInfo.data?.member_count || 0,
        online_count: discordInfo.data?.presence_count || 0,
        activity_ratio: this.calculateDiscordActivity(discordInfo.data),
        fallback_used: discordInfo.fallback || false,
      };
    }

    // Calculate overall sentiment and confidence
    const sentimentAnalysis = this.calculateOverallSentiment(results);
    results.overall_sentiment = sentimentAnalysis.sentiment;
    results.confidence = sentimentAnalysis.confidence;

    return {
      success: true,
      data: results,
      timestamp: new Date().toISOString(),
    };
  }

  private analyzeTweetSentiment(tweets: any[]): any {
    if (!tweets.length) {
      return { sentiment: 'neutral', confidence: 0 };
    }

    const positiveKeywords = ['great', 'awesome', 'amazing', 'love', 'excellent', 'good', 'best', 'fantastic'];
    const negativeKeywords = ['bad', 'terrible', 'awful', 'hate', 'worst', 'scam', 'rug', 'dead'];

    let positiveCount = 0;
    let negativeCount = 0;
    let totalCount = tweets.length;

    tweets.forEach(tweet => {
      const text = tweet.text?.toLowerCase() || '';
      positiveKeywords.forEach(keyword => {
        if (text.includes(keyword)) positiveCount++;
      });
      negativeKeywords.forEach(keyword => {
        if (text.includes(keyword)) negativeCount++;
      });
    });

    const sentiment = positiveCount > negativeCount ? 'positive' : 
                     negativeCount > positiveCount ? 'negative' : 'neutral';
    
    const confidence = totalCount > 0 ? Math.min((positiveCount + negativeCount) / totalCount, 1) : 0;

    return {
      sentiment,
      confidence,
      positive_signals: positiveCount,
      negative_signals: negativeCount,
      total_analyzed: totalCount,
    };
  }

  private calculateTwitterEngagement(profile: any, tweets: any[]): any {
    if (!profile || !tweets.length) {
      return { engagement_rate: 0, avg_likes: 0, avg_retweets: 0 };
    }

    const totalLikes = tweets.reduce((sum, tweet) => sum + (tweet.like_count || 0), 0);
    const totalRetweets = tweets.reduce((sum, tweet) => sum + (tweet.retweet_count || 0), 0);
    const totalReplies = tweets.reduce((sum, tweet) => sum + (tweet.reply_count || 0), 0);
    
    const avgLikes = tweets.length > 0 ? totalLikes / tweets.length : 0;
    const avgRetweets = tweets.length > 0 ? totalRetweets / tweets.length : 0;
    const avgReplies = tweets.length > 0 ? totalReplies / tweets.length : 0;
    
    const avgEngagement = avgLikes + avgRetweets + avgReplies;
    const engagementRate = profile.followers_count > 0 ? (avgEngagement / profile.followers_count) * 100 : 0;

    return {
      engagement_rate: Math.round(engagementRate * 100) / 100,
      avg_likes: Math.round(avgLikes),
      avg_retweets: Math.round(avgRetweets),
      avg_replies: Math.round(avgReplies),
      total_engagement: Math.round(avgEngagement),
    };
  }

  private estimateActivityLevel(memberCount: number): string {
    if (memberCount > 10000) return 'high';
    if (memberCount > 1000) return 'medium';
    if (memberCount > 100) return 'low';
    return 'very_low';
  }

  private calculateDiscordActivity(serverData: any): number {
    if (!serverData?.member_count || !serverData?.presence_count) return 0;
    return Math.round((serverData.presence_count / serverData.member_count) * 100) / 100;
  }

  private calculateOverallSentiment(results: any): any {
    let totalSentimentScore = 0;
    let platformCount = 0;
    let totalConfidence = 0;

    // Twitter sentiment
    if (results.twitter?.sentiment_analysis) {
      const twitterSentiment = results.twitter.sentiment_analysis.sentiment;
      const score = twitterSentiment === 'positive' ? 1 : twitterSentiment === 'negative' ? -1 : 0;
      totalSentimentScore += score;
      totalConfidence += results.twitter.sentiment_analysis.confidence;
      platformCount++;
    }

    // Telegram activity as sentiment indicator
    if (results.telegram?.activity_level) {
      const activityScore = results.telegram.activity_level === 'high' ? 0.5 : 
                           results.telegram.activity_level === 'medium' ? 0 : -0.5;
      totalSentimentScore += activityScore;
      totalConfidence += 0.3; // Lower confidence for activity-based sentiment
      platformCount++;
    }

    // Discord activity as sentiment indicator
    if (results.discord?.activity_ratio) {
      const activityScore = results.discord.activity_ratio > 0.3 ? 0.5 : 
                           results.discord.activity_ratio > 0.1 ? 0 : -0.5;
      totalSentimentScore += activityScore;
      totalConfidence += 0.3;
      platformCount++;
    }

    if (platformCount === 0) {
      return { sentiment: 'neutral', confidence: 0 };
    }

    const avgScore = totalSentimentScore / platformCount;
    const avgConfidence = totalConfidence / platformCount;

    const sentiment = avgScore > 0.2 ? 'positive' : 
                     avgScore < -0.2 ? 'negative' : 'neutral';

    return {
      sentiment,
      confidence: Math.round(avgConfidence * 100) / 100,
    };
  }

  async rateLimitedRequest(url: string, options: any, delay: number = 1000): Promise<any> {
    await new Promise(resolve => setTimeout(resolve, delay));
    return fetch(url, options);
  }
}