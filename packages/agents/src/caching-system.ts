import { z } from 'zod';
import { supabase } from '@cma/database';

const CacheEntrySchema = z.object({
  key: z.string(),
  value: z.any(),
  expiry: z.number(),
  tags: z.array(z.string()).optional(),
  metadata: z.record(z.any()).optional(),
});

export type CacheEntry = z.infer<typeof CacheEntrySchema>;

export interface CacheOptions {
  ttl?: number; // Time to live in seconds
  tags?: string[];
  metadata?: Record<string, any>;
  strategy?: 'memory' | 'database' | 'hybrid';
}

export interface CacheStats {
  total_entries: number;
  memory_entries: number;
  database_entries: number;
  hits: number;
  misses: number;
  hit_ratio: number;
  total_size_mb: number;
}

export class CachingSystem {
  private memoryCache: Map<string, CacheEntry> = new Map();
  private hitCount: number = 0;
  private missCount: number = 0;
  private maxMemoryEntries: number = 1000;
  private defaultTTL: number = 3600; // 1 hour in seconds

  constructor(maxMemoryEntries: number = 1000, defaultTTL: number = 3600) {
    this.maxMemoryEntries = maxMemoryEntries;
    this.defaultTTL = defaultTTL;
    
    // Start cleanup interval
    this.startCleanupInterval();
  }

  async get<T>(key: string): Promise<T | null> {
    try {
      // Try memory cache first
      const memoryEntry = this.memoryCache.get(key);
      if (memoryEntry && this.isEntryValid(memoryEntry)) {
        this.hitCount++;
        return memoryEntry.value as T;
      }

      // Try database cache
      const { data, error } = await supabase
        .from('cache_entries')
        .select('*')
        .eq('key', key)
        .single();

      if (error || !data) {
        this.missCount++;
        return null;
      }

      const dbEntry: CacheEntry = {
        key: data.key,
        value: data.value,
        expiry: data.expiry,
        tags: data.tags || [],
        metadata: data.metadata || {},
      };

      if (!this.isEntryValid(dbEntry)) {
        // Entry expired, remove it
        await this.delete(key);
        this.missCount++;
        return null;
      }

      // Store in memory cache for faster future access
      this.setMemoryCache(key, dbEntry);
      this.hitCount++;
      return dbEntry.value as T;

    } catch (error) {
      console.error('Cache get error:', error);
      this.missCount++;
      return null;
    }
  }

  async set<T>(key: string, value: T, options: CacheOptions = {}): Promise<void> {
    const ttl = options.ttl || this.defaultTTL;
    const expiry = Date.now() + (ttl * 1000);
    
    const entry: CacheEntry = {
      key,
      value,
      expiry,
      tags: options.tags || [],
      metadata: options.metadata || {},
    };

    const strategy = options.strategy || 'hybrid';

    try {
      // Store in memory cache
      if (strategy === 'memory' || strategy === 'hybrid') {
        this.setMemoryCache(key, entry);
      }

      // Store in database cache
      if (strategy === 'database' || strategy === 'hybrid') {
        await this.setDatabaseCache(entry);
      }

    } catch (error) {
      console.error('Cache set error:', error);
      throw new Error('Failed to set cache entry');
    }
  }

  async delete(key: string): Promise<void> {
    try {
      // Remove from memory cache
      this.memoryCache.delete(key);

      // Remove from database cache
      const { error } = await supabase
        .from('cache_entries')
        .delete()
        .eq('key', key);

      if (error) {
        console.error('Database cache delete error:', error);
      }

    } catch (error) {
      console.error('Cache delete error:', error);
    }
  }

  async clear(): Promise<void> {
    try {
      // Clear memory cache
      this.memoryCache.clear();

      // Clear database cache
      const { error } = await supabase
        .from('cache_entries')
        .delete()
        .neq('key', ''); // Delete all entries

      if (error) {
        console.error('Database cache clear error:', error);
      }

    } catch (error) {
      console.error('Cache clear error:', error);
    }
  }

  async invalidateByTags(tags: string[]): Promise<void> {
    try {
      // Remove from memory cache
      for (const [key, entry] of this.memoryCache.entries()) {
        if (entry.tags && entry.tags.some(tag => tags.includes(tag))) {
          this.memoryCache.delete(key);
        }
      }

      // Remove from database cache
      for (const tag of tags) {
        const { error } = await supabase
          .from('cache_entries')
          .delete()
          .contains('tags', [tag]);

        if (error) {
          console.error('Database cache tag invalidation error:', error);
        }
      }

    } catch (error) {
      console.error('Cache tag invalidation error:', error);
    }
  }

  async invalidateByPattern(pattern: string): Promise<void> {
    try {
      const regex = new RegExp(pattern);

      // Remove from memory cache
      for (const key of this.memoryCache.keys()) {
        if (regex.test(key)) {
          this.memoryCache.delete(key);
        }
      }

      // Remove from database cache
      const { data, error } = await supabase
        .from('cache_entries')
        .select('key')
        .like('key', `%${pattern}%`);

      if (error) {
        console.error('Database cache pattern query error:', error);
        return;
      }

      for (const entry of data || []) {
        if (regex.test(entry.key)) {
          await this.delete(entry.key);
        }
      }

    } catch (error) {
      console.error('Cache pattern invalidation error:', error);
    }
  }

  async getStats(): Promise<CacheStats> {
    try {
      const memoryEntries = this.memoryCache.size;
      
      const { count: databaseEntries, error } = await supabase
        .from('cache_entries')
        .select('*', { count: 'exact', head: true });

      if (error) {
        console.error('Database cache stats error:', error);
      }

      const totalEntries = memoryEntries + (databaseEntries || 0);
      const totalRequests = this.hitCount + this.missCount;
      const hitRatio = totalRequests > 0 ? this.hitCount / totalRequests : 0;

      // Calculate approximate memory size
      let totalSizeMB = 0;
      for (const entry of this.memoryCache.values()) {
        totalSizeMB += this.calculateEntrySize(entry);
      }
      totalSizeMB = totalSizeMB / (1024 * 1024); // Convert to MB

      return {
        total_entries: totalEntries,
        memory_entries: memoryEntries,
        database_entries: databaseEntries || 0,
        hits: this.hitCount,
        misses: this.missCount,
        hit_ratio: Math.round(hitRatio * 100) / 100,
        total_size_mb: Math.round(totalSizeMB * 100) / 100,
      };

    } catch (error) {
      console.error('Cache stats error:', error);
      return {
        total_entries: 0,
        memory_entries: 0,
        database_entries: 0,
        hits: this.hitCount,
        misses: this.missCount,
        hit_ratio: 0,
        total_size_mb: 0,
      };
    }
  }

  // Specialized caching methods for different data types

  async cacheApiResponse<T>(
    endpoint: string,
    params: Record<string, any>,
    response: T,
    ttl: number = 300 // 5 minutes default for API responses
  ): Promise<void> {
    const key = this.generateApiCacheKey(endpoint, params);
    await this.set(key, response, {
      ttl,
      tags: ['api', endpoint],
      metadata: { endpoint, params, cached_at: new Date().toISOString() },
    });
  }

  async getCachedApiResponse<T>(
    endpoint: string,
    params: Record<string, any>
  ): Promise<T | null> {
    const key = this.generateApiCacheKey(endpoint, params);
    return await this.get<T>(key);
  }

  async cacheAgentResult<T>(
    agentName: string,
    projectId: string,
    input: any,
    result: T,
    ttl: number = 1800 // 30 minutes default for agent results
  ): Promise<void> {
    const key = this.generateAgentCacheKey(agentName, projectId, input);
    await this.set(key, result, {
      ttl,
      tags: ['agent', agentName, `project:${projectId}`],
      metadata: { 
        agent_name: agentName,
        project_id: projectId,
        input_hash: this.hashObject(input),
        cached_at: new Date().toISOString(),
      },
    });
  }

  async getCachedAgentResult<T>(
    agentName: string,
    projectId: string,
    input: any
  ): Promise<T | null> {
    const key = this.generateAgentCacheKey(agentName, projectId, input);
    return await this.get<T>(key);
  }

  async cacheBlockchainData<T>(
    protocol: string,
    dataType: string,
    data: T,
    ttl: number = 600 // 10 minutes default for blockchain data
  ): Promise<void> {
    const key = `blockchain:${protocol}:${dataType}`;
    await this.set(key, data, {
      ttl,
      tags: ['blockchain', protocol, dataType],
      metadata: { 
        protocol,
        data_type: dataType,
        cached_at: new Date().toISOString(),
      },
    });
  }

  async getCachedBlockchainData<T>(
    protocol: string,
    dataType: string
  ): Promise<T | null> {
    const key = `blockchain:${protocol}:${dataType}`;
    return await this.get<T>(key);
  }

  async cacheSocialMediaData<T>(
    platform: string,
    username: string,
    dataType: string,
    data: T,
    ttl: number = 900 // 15 minutes default for social media data
  ): Promise<void> {
    const key = `social:${platform}:${username}:${dataType}`;
    await this.set(key, data, {
      ttl,
      tags: ['social', platform, dataType],
      metadata: { 
        platform,
        username,
        data_type: dataType,
        cached_at: new Date().toISOString(),
      },
    });
  }

  async getCachedSocialMediaData<T>(
    platform: string,
    username: string,
    dataType: string
  ): Promise<T | null> {
    const key = `social:${platform}:${username}:${dataType}`;
    return await this.get<T>(key);
  }

  async cacheWebsiteContent<T>(
    url: string,
    content: T,
    ttl: number = 3600 // 1 hour default for website content
  ): Promise<void> {
    const key = `website:${this.hashString(url)}`;
    await this.set(key, content, {
      ttl,
      tags: ['website', 'content'],
      metadata: { 
        url,
        cached_at: new Date().toISOString(),
      },
    });
  }

  async getCachedWebsiteContent<T>(url: string): Promise<T | null> {
    const key = `website:${this.hashString(url)}`;
    return await this.get<T>(key);
  }

  // Cache warming and preloading
  
  async warmCache(projectId: string): Promise<void> {
    try {
      // Pre-cache commonly requested data for a project
      const warmupTasks = [
        this.warmupBlockchainData(projectId),
        this.warmupSocialMediaData(projectId),
        this.warmupMarketData(projectId),
      ];

      await Promise.allSettled(warmupTasks);
    } catch (error) {
      console.error('Cache warming error:', error);
    }
  }

  private async warmupBlockchainData(projectId: string): Promise<void> {
    // Pre-cache popular blockchain metrics
    const popularProtocols = ['uniswap', 'aave', 'compound', 'makerdao'];
    const dataTypes = ['tvl', 'volume', 'fees'];
    
    for (const protocol of popularProtocols) {
      for (const dataType of dataTypes) {
        const cacheKey = `blockchain:${protocol}:${dataType}`;
        const cached = await this.get(cacheKey);
        if (!cached) {
          // Cache would be populated by actual blockchain service calls
          console.log(`Warming cache for ${protocol}:${dataType}`);
        }
      }
    }
  }

  private async warmupSocialMediaData(projectId: string): Promise<void> {
    // Pre-cache popular social media metrics
    console.log(`Warming social media cache for project ${projectId}`);
  }

  private async warmupMarketData(projectId: string): Promise<void> {
    // Pre-cache market data
    console.log(`Warming market data cache for project ${projectId}`);
  }

  // Private helper methods

  private setMemoryCache(key: string, entry: CacheEntry): void {
    // Implement LRU eviction if memory cache is full
    if (this.memoryCache.size >= this.maxMemoryEntries) {
      const firstKey = this.memoryCache.keys().next().value;
      if (firstKey) {
        this.memoryCache.delete(firstKey);
      }
    }
    
    this.memoryCache.set(key, entry);
  }

  private async setDatabaseCache(entry: CacheEntry): Promise<void> {
    const { error } = await supabase
      .from('cache_entries')
      .upsert({
        key: entry.key,
        value: entry.value,
        expiry: entry.expiry,
        tags: entry.tags || [],
        metadata: entry.metadata || {},
        created_at: new Date().toISOString(),
      });

    if (error) {
      console.error('Database cache set error:', error);
      throw new Error('Failed to set database cache entry');
    }
  }

  private isEntryValid(entry: CacheEntry): boolean {
    return Date.now() < entry.expiry;
  }

  private generateApiCacheKey(endpoint: string, params: Record<string, any>): string {
    const sortedParams = Object.keys(params)
      .sort()
      .reduce((result, key) => {
        result[key] = params[key];
        return result;
      }, {} as Record<string, any>);
    
    const paramsString = JSON.stringify(sortedParams);
    return `api:${endpoint}:${this.hashString(paramsString)}`;
  }

  private generateAgentCacheKey(agentName: string, projectId: string, input: any): string {
    const inputHash = this.hashObject(input);
    return `agent:${agentName}:${projectId}:${inputHash}`;
  }

  private hashString(str: string): string {
    let hash = 0;
    if (str.length === 0) return hash.toString();
    
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    
    return Math.abs(hash).toString(36);
  }

  private hashObject(obj: any): string {
    return this.hashString(JSON.stringify(obj));
  }

  private calculateEntrySize(entry: CacheEntry): number {
    // Rough estimation of entry size in bytes
    return JSON.stringify(entry).length * 2; // Approximate UTF-16 encoding
  }

  private startCleanupInterval(): void {
    // Clean up expired entries every 5 minutes
    setInterval(async () => {
      await this.cleanupExpiredEntries();
    }, 5 * 60 * 1000);
  }

  private async cleanupExpiredEntries(): Promise<void> {
    try {
      // Clean memory cache
      const now = Date.now();
      for (const [key, entry] of this.memoryCache.entries()) {
        if (now >= entry.expiry) {
          this.memoryCache.delete(key);
        }
      }

      // Clean database cache
      const { error } = await supabase
        .from('cache_entries')
        .delete()
        .lt('expiry', now);

      if (error) {
        console.error('Database cache cleanup error:', error);
      }

    } catch (error) {
      console.error('Cache cleanup error:', error);
    }
  }

  // Batch operations

  async setMany<T>(entries: Array<{ key: string; value: T; options?: CacheOptions }>): Promise<void> {
    const promises = entries.map(entry => 
      this.set(entry.key, entry.value, entry.options)
    );
    
    await Promise.allSettled(promises);
  }

  async getMany<T>(keys: string[]): Promise<Array<{ key: string; value: T | null }>> {
    const promises = keys.map(async key => ({
      key,
      value: await this.get<T>(key),
    }));
    
    return Promise.all(promises);
  }

  async deleteMany(keys: string[]): Promise<void> {
    const promises = keys.map(key => this.delete(key));
    await Promise.allSettled(promises);
  }
}

// Singleton instance
export const cacheSystem = new CachingSystem();