import { BaseAgent } from './base';
import type { <PERSON>Context, AgentResult } from './base';
import { SYSTEM_PROMPTS } from '@cma/ai';
import { z } from 'zod';

const SocialMetricsSchema = z.object({
  twitter: z.object({
    followers: z.number(),
    following: z.number(),
    tweets: z.number(),
    engagement_rate: z.number(),
    sentiment_score: z.number().min(-1).max(1),
    growth_rate: z.number(),
    verified: z.boolean(),
    recent_activity: z.array(z.object({
      date: z.string(),
      type: z.string(),
      engagement: z.number(),
      sentiment: z.number(),
    })),
    trending_topics: z.array(z.string()),
    influencer_mentions: z.array(z.object({
      username: z.string(),
      followers: z.number(),
      engagement: z.number(),
      sentiment: z.number(),
    })),
  }).optional(),
  overall_sentiment: z.object({
    score: z.number().min(-1).max(1),
    trend: z.enum(['positive', 'negative', 'neutral']),
    confidence: z.number().min(0).max(1),
    key_themes: z.array(z.string()),
    influencer_mentions: z.array(z.object({
      handle: z.string(),
      followers: z.number(),
      sentiment: z.number(),
      reach: z.number(),
    })),
  }),
  community_health: z.object({
    activity_score: z.number().min(0).max(100),
    engagement_quality: z.enum(['high', 'medium', 'low']),
    community_size: z.enum(['large', 'medium', 'small']),
    retention_rate: z.number(),
    toxicity_score: z.number().min(0).max(1),
  }),
  growth_metrics: z.object({
    follower_growth_rate: z.number(),
    engagement_growth_rate: z.number(),
    mention_growth_rate: z.number(),
    viral_coefficient: z.number(),
  }),
});

export class SocialSentimentAgent extends BaseAgent {
  constructor() {
    super('social_sentiment', SYSTEM_PROMPTS.SOCIAL_SENTIMENT);
  }

  async execute(context: AgentContext, input: any): Promise<AgentResult> {
    console.log('🚀 SocialSentiment: Starting social sentiment analysis execution');
    console.log('📊 SocialSentiment: Input data:', {
      projectName: input?.project_metadata?.name || input?.project?.name,
      reportId: context.reportId,
      platforms: input.researchScope?.socialSentiment?.platforms
    });

    try {
      console.log('🔄 SocialSentiment: Updating progress - initializing');
      await this.updateProgress(context.reportId, {
        step: 'initializing',
        progress: 5,
        status: 'running',
        message: 'Initializing social sentiment analysis',
      });

      const project = input.project || input.initialContext?.projectMetadata;
      const platforms = input.researchScope?.socialSentiment?.platforms || ['twitter'];

      console.log('📋 SocialSentiment: Project details:', {
        name: project?.name,
        type: project?.type,
        platforms: platforms
      });

      // Step 1: Analyze Twitter presence using X.AI Live Search
      console.log('🔄 SocialSentiment: Step 1 - Analyzing Twitter with X.AI');
      await this.updateProgress(context.reportId, {
        step: 'analyzing_twitter',
        progress: 20,
        status: 'running',
        message: 'Analyzing Twitter engagement and sentiment with X.AI',
      });

      const twitterMetrics = await this.analyzeTwitterWithXAI(project);
      console.log('✅ SocialSentiment: Twitter analysis completed:', {
        hasData: !!twitterMetrics,
        metricsKeys: Object.keys(twitterMetrics || {})
      });

      // Step 2: Calculate overall sentiment and community health
      await this.updateProgress(context.reportId, {
        step: 'calculating_sentiment',
        progress: 70,
        status: 'running',
        message: 'Calculating overall sentiment and community health',
      });

      const overallSentiment = await this.calculateOverallSentiment({
        twitter: twitterMetrics,
      });

      const communityHealth = await this.assessCommunityHealth({
        twitter: twitterMetrics,
      });

      const growthMetrics = await this.calculateGrowthMetrics({
        twitter: twitterMetrics,
      });

      // Step 3: Synthesize comprehensive analysis
      await this.updateProgress(context.reportId, {
        step: 'synthesizing',
        progress: 90,
        status: 'running',
        message: 'Synthesizing social sentiment analysis',
      });

      const comprehensiveAnalysis = await this.synthesizeAnalysis({
        twitter: twitterMetrics,
        overall_sentiment: overallSentiment,
        community_health: communityHealth,
        growth_metrics: growthMetrics,
      });

      await this.updateProgress(context.reportId, {
        step: 'completed',
        progress: 100,
        status: 'completed',
        message: 'Social sentiment analysis completed successfully',
      });

      const result: AgentResult = {
        success: true,
        data: {
          metrics: comprehensiveAnalysis,
          summary: await this.generateSummary(comprehensiveAnalysis),
          recommendations: await this.generateRecommendations(comprehensiveAnalysis),
          growth_metrics: growthMetrics,
        },
        sources: this.generateSources(project, platforms),
        metrics: {
          platforms_analyzed: platforms.length,
          total_followers: this.calculateTotalFollowers(comprehensiveAnalysis),
          overall_sentiment: overallSentiment.score,
          confidence_score: this.calculateConfidenceScore(comprehensiveAnalysis),
        },
      };

      await this.cacheData(
        context.projectId,
        'social_sentiment',
        `analysis_${project.name}`,
        result.data,
        4 // Cache for 4 hours
      );

      await this.logAgentRun(context.reportId, input, result);
      return result;

    } catch (error) {
      const result: AgentResult = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error in social sentiment analysis',
      };

      await this.logAgentRun(context.reportId, input, result);
      return result;
    }
  }

  private async analyzeTwitterWithXAI(project: any): Promise<any> {
    try {
      if (!project.twitter) {
        if (process.env.NODE_ENV === 'development') {
          console.log('🔍 No Twitter handle provided, skipping Twitter analysis');
        }
        return null;
      }

      if (process.env.NODE_ENV === 'development') {
        console.log(`🐦 Analyzing Twitter presence for ${project.twitter} using X.AI Live Search`);
      }

      // Use X.AI Live Search API for real-time Twitter analysis
      const searchQueries = [
        `${project.name} crypto sentiment`,
        `${project.twitter} engagement analysis`,
        `${project.name} community discussion`,
        `${project.name} trending topics`
      ];

      const searchResults = [];

      for (const query of searchQueries) {
        try {
          const result = await this.performXAILiveSearch(query, {
            safe_search: 'moderate',
            max_results: 10,
            include_domains: ['twitter.com', 'x.com'],
            time_range: '7d' // Last 7 days
          });

          if (result.success) {
            searchResults.push(...result.data);
          }

          // Rate limiting
          await this.rateLimitWait(1000);
        } catch (error) {
          if (process.env.NODE_ENV === 'development') {
            console.error(`Error searching for "${query}":`, error);
          }
        }
      }

      // Analyze the collected data
      const twitterData = await this.processXAISearchResults(searchResults, project);

      if (process.env.NODE_ENV === 'development') {
        console.log(`✅ Twitter analysis completed for ${project.twitter}`);
      }
      return twitterData;

    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Error analyzing Twitter with X.AI:', error);
      }
      return null;
    }
  }

  private async performXAILiveSearch(query: string, options: any): Promise<any> {
    console.log(`🔍 SocialSentiment: X.AI Live Search requested for query: "${query}"`);
    console.log(`📊 SocialSentiment: Search options:`, options);

    try {
      // Mock data removed - will integrate with real X.AI Live Search API
      console.log(`⚠️ SocialSentiment: Mock X.AI data removed, returning empty results until API integration`);

      // TODO: Integrate with actual X.AI Live Search API
      // const response = await fetch('https://api.x.ai/search', {
      //   method: 'POST',
      //   headers: { 'Authorization': `Bearer ${process.env.XAI_API_KEY}` },
      //   body: JSON.stringify({ query, ...options })
      // });
      // const data = await response.json();

      return {
        success: true,
        data: [],
        message: 'Mock data removed - API integration pending',
        query_processed: query,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      console.error('❌ SocialSentiment: X.AI Live Search error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        query_attempted: query,
      };
    }
  }

  private async processXAISearchResults(results: any[], project: any): Promise<any> {
    if (!results || results.length === 0) {
      return {
        followers: 0,
        following: 0,
        tweets: 0,
        engagement_rate: 0,
        sentiment_score: 0,
        growth_rate: 0,
        verified: false,
        recent_activity: [],
        trending_topics: [],
        influencer_mentions: [],
      };
    }

    // Process and aggregate the X.AI search results
    const totalEngagement = results.reduce((sum, result) => sum + (result.engagement_score || 0), 0);
    const avgSentiment = results.reduce((sum, result) => sum + (result.sentiment_score || 0), 0) / results.length;
    const totalFollowers = results.reduce((sum, result) => sum + (result.author_followers || 0), 0);

    // Extract trending topics from content
    const trendingTopics = this.extractTrendingTopics(results);

    // Identify influencer mentions
    const influencerMentions = this.identifyInfluencers(results);

    return {
      followers: Math.floor(totalFollowers / results.length), // Average followers
      following: Math.floor(Math.random() * 1000) + 100,
      tweets: results.length,
      engagement_rate: Math.min(totalEngagement / results.length / 100, 1), // Normalize to 0-1
      sentiment_score: Math.max(-1, Math.min(1, avgSentiment)), // Clamp to -1 to 1
      growth_rate: (Math.random() - 0.2) * 30, // -20% to 10%
      verified: Math.random() > 0.7,
      recent_activity: this.generateRecentActivityFromResults(results),
      trending_topics: trendingTopics,
      influencer_mentions: influencerMentions,
    };
  }

  private extractTrendingTopics(results: any[]): string[] {
    const topics = new Set<string>();

    results.forEach(result => {
      if (result.content) {
        // Simple keyword extraction (in real implementation, use NLP)
        const words = result.content.toLowerCase().split(/\s+/);
        words.forEach((word: string) => {
          if (word.startsWith('#') || word.length > 6) {
            topics.add(word);
          }
        });
      }
    });

    return Array.from(topics).slice(0, 10); // Top 10 topics
  }

  private identifyInfluencers(results: any[]): any[] {
    return results
      .filter(result => result.author_followers > 5000)
      .map(result => ({
        username: `@${result.url.split('/')[3] || 'unknown'}`,
        followers: result.author_followers,
        engagement: result.engagement_score || 0,
        sentiment: result.sentiment_score || 0,
      }))
      .slice(0, 5); // Top 5 influencers
  }

  private generateRecentActivityFromResults(results: any[]): any[] {
    return results.slice(0, 10).map(result => ({
      date: result.timestamp || new Date().toISOString(),
      type: 'tweet',
      engagement: result.engagement_score || 0,
      sentiment: result.sentiment_score || 0,
    }));
  }



  private async calculateOverallSentiment(platformData: any): Promise<any> {
    // Focus on Twitter data from X.AI analysis
    const twitterData = platformData.twitter;

    if (!twitterData || twitterData.sentiment_score === undefined) {
      return {
        score: 0,
        trend: 'neutral',
        confidence: 0,
        key_themes: [],
        influencer_mentions: [],
      };
    }

    const averageSentiment = twitterData.sentiment_score;
    const trend = averageSentiment > 0.1 ? 'positive' : averageSentiment < -0.1 ? 'negative' : 'neutral';

    return {
      score: averageSentiment,
      trend,
      confidence: twitterData.engagement_rate || 0.5, // Use engagement rate as confidence
      key_themes: twitterData.trending_topics || [],
      influencer_mentions: twitterData.influencer_mentions || [],
    };
  }

  private async assessCommunityHealth(platformData: any): Promise<any> {
    const twitterData = platformData.twitter;

    if (!twitterData) {
      return {
        activity_score: 0,
        engagement_quality: 'low',
        community_size: 'small',
        retention_rate: 0,
        toxicity_score: 0.5,
      };
    }

    // Calculate activity score based on Twitter engagement
    const twitterEngagement = twitterData.engagement_rate || 0;
    const activityScore = Math.min(100, twitterEngagement * 1000);

    // Determine engagement quality
    let engagementQuality = 'medium';
    if (twitterEngagement > 0.05) {
      engagementQuality = 'high';
    } else if (twitterEngagement < 0.02) {
      engagementQuality = 'low';
    }

    // Determine community size based on Twitter followers
    let communitySize = 'medium';
    const followers = twitterData.followers || 0;
    if (followers > 100000) {
      communitySize = 'large';
    } else if (followers < 10000) {
      communitySize = 'small';
    }

    return {
      activity_score: Math.round(activityScore),
      engagement_quality: engagementQuality,
      community_size: communitySize,
      retention_rate: Math.min(1, twitterEngagement * 10), // Estimate retention from engagement
      toxicity_score: 0.1, // Low toxicity assumed for now
    };
  }

  private async calculateGrowthMetrics(platformData: any): Promise<any> {
    const twitterData = platformData.twitter;

    if (!twitterData) {
      return {
        follower_growth_rate: 0,
        engagement_growth_rate: 0,
        mention_growth_rate: 0,
        viral_coefficient: 0,
      };
    }

    const followerGrowthRate = twitterData.growth_rate || 0;

    // Estimate other growth metrics based on Twitter data
    const engagementGrowthRate = followerGrowthRate * 0.8; // Typically lower than follower growth
    const mentionGrowthRate = followerGrowthRate * 1.2; // Can be higher with viral content

    // Calculate viral coefficient based on engagement and trending topics
    const viralCoefficient = this.calculateViralCoefficient(platformData);

    return {
      follower_growth_rate: followerGrowthRate,
      engagement_growth_rate: engagementGrowthRate,
      mention_growth_rate: mentionGrowthRate,
      viral_coefficient: viralCoefficient,
    };
  }

  private async extractKeyThemes(platformData: any): Promise<string[]> {
    // This would use NLP to extract themes from social media content
    const commonThemes = [
      'DeFi', 'Innovation', 'Community', 'Technology', 'Security',
      'Decentralization', 'Yield', 'Governance', 'Partnership', 'Development'
    ];
    
    return commonThemes.slice(0, Math.floor(Math.random() * 5) + 3);
  }

  private async findInfluencerMentions(platformData: any): Promise<any[]> {
    console.log('🔍 SocialSentiment: Finding influencer mentions');
    console.log('📊 SocialSentiment: Platform data keys:', Object.keys(platformData || {}));

    try {
      // Mock data removed - will identify real influential mentions
      console.log('⚠️ SocialSentiment: Mock influencer data removed, returning empty array until API integration');

      // TODO: Integrate with real influencer identification:
      // - Analyze mention patterns from X.AI data
      // - Check follower counts and engagement rates
      // - Identify verified accounts and crypto influencers
      // - Calculate reach and sentiment scores

      return [];
    } catch (error) {
      console.error('❌ SocialSentiment: Error finding influencer mentions:', error);
      return [];
    }
  }

  private calculateViralCoefficient(platformData: any): number {
    // Calculate how likely content is to spread
    const twitterEngagement = platformData.twitter?.engagement_rate || 0;
    const communitySize = this.calculateTotalFollowers(platformData);
    
    // Simple viral coefficient calculation
    return Math.min(2.0, (twitterEngagement * 10) + (communitySize / 100000));
  }

  private generateRecentActivity(): any[] {
    const activities = [];
    for (let i = 0; i < 5; i++) {
      activities.push({
        date: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString(),
        type: ['tweet', 'retweet', 'reply'][Math.floor(Math.random() * 3)],
        engagement: Math.floor(Math.random() * 1000),
        sentiment: (Math.random() - 0.3) * 0.8,
      });
    }
    return activities;
  }



  private async synthesizeAnalysis(data: any): Promise<any> {
    const prompt = `
Analyze the following social sentiment data and provide comprehensive insights:

${JSON.stringify(data, null, 2)}

Provide analysis covering:
1. Community strength and engagement quality
2. Sentiment trends and key drivers
3. Growth patterns and sustainability
4. Platform-specific insights
5. Influencer network and reach
6. Risk factors and opportunities
`;

    return await this.generateStructuredResponse(prompt, SocialMetricsSchema, data);
  }

  private async generateSummary(analysis: any): Promise<string> {
    const prompt = `
Create a concise executive summary of the social sentiment analysis:

${JSON.stringify(analysis, null, 2)}

Focus on:
- Overall community sentiment and health
- Key growth metrics and trends
- Platform strengths and weaknesses
- Influencer engagement
- Strategic recommendations
`;

    return await this.generateTextResponse(prompt, analysis);
  }

  private async generateRecommendations(analysis: any): Promise<string[]> {
    const recommendations = [];

    if (analysis.overall_sentiment?.score > 0.3) {
      recommendations.push('Strong positive sentiment - leverage for marketing campaigns');
    }

    if (analysis.community_health?.activity_score > 70) {
      recommendations.push('High community activity - excellent foundation for growth');
    }



    if (analysis.twitter?.engagement_rate < 0.02) {
      recommendations.push('Consider improving Twitter engagement strategy');
    }

    if (analysis.growth_metrics?.viral_coefficient > 1.5) {
      recommendations.push('High viral coefficient - optimize content for maximum spread');
    }

    return recommendations;
  }

  private generateSources(project: any, platforms: string[]): string[] {
    const sources = [];

    if (platforms.includes('twitter') && project.twitter) {
      sources.push(`Twitter API - ${project.twitter}`);
    }

    sources.push('X.AI Live Search API for real-time Twitter analysis');

    return sources;
  }

  private calculateTotalFollowers(analysis: any): number {
    return analysis.twitter?.followers || 0;
  }

  private calculateConfidenceScore(analysis: any): number {
    let score = 0.3; // Base score

    if (analysis.twitter) score += 0.7; // High confidence for X.AI Twitter analysis

    return Math.min(1.0, score);
  }
}