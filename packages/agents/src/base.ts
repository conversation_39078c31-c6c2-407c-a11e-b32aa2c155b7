import { generateObject, generateText } from 'ai';
import { getModel } from '@cma/ai';
import { createServerClient } from '@cma/database';
import { z } from 'zod';

export interface AgentContext {
  projectId: string;
  reportId: string;
  userId: string;
  workspaceId: string;
  reportDepth: 'quick' | 'standard' | 'deep';
}

export interface AgentResult {
  success: boolean;
  data?: any;
  error?: string;
  sources?: string[];
  metrics?: Record<string, any>;
}

export interface AgentProgress {
  step: string;
  progress: number;
  status: 'pending' | 'running' | 'completed' | 'failed';
  message?: string;
}

export abstract class BaseAgent {
  protected db = createServerClient();
  protected agentType: string;
  protected systemPrompt: string;

  constructor(agentType: string, systemPrompt: string) {
    this.agentType = agentType;
    this.systemPrompt = systemPrompt;
  }

  abstract execute(context: AgentContext, input: any): Promise<AgentResult>;

  protected async updateProgress(
    reportId: string,
    progress: AgentProgress
  ): Promise<void> {
    try {
      await this.db
        .from('agent_runs')
        .update({
          status: progress.status,
          metrics: {
            step: progress.step,
            progress: progress.progress,
            message: progress.message,
          },
          updated_at: new Date().toISOString(),
          ...(progress.status === 'running' && { started_at: new Date().toISOString() }),
          ...(progress.status === 'completed' && { completed_at: new Date().toISOString() }),
        })
        .eq('report_id', reportId)
        .eq('agent_type', this.agentType);
    } catch (error) {
      console.error(`Failed to update progress for ${this.agentType}:`, error);
    }
  }

  protected async logAgentRun(
    reportId: string,
    input: any,
    result: AgentResult
  ): Promise<void> {
    try {
      await this.db.from('agent_runs').insert({
        report_id: reportId,
        agent_type: this.agentType,
        status: result.success ? 'completed' : 'failed',
        input_data: input,
        output_data: result.data,
        error_message: result.error,
        metrics: result.metrics,
        completed_at: new Date().toISOString(),
      });
    } catch (error) {
      console.error(`Failed to log agent run for ${this.agentType}:`, error);
    }
  }

  protected async generateStructuredResponse<T>(
    prompt: string,
    schema: z.ZodSchema<T>,
    context?: any
  ): Promise<T> {
    console.log(`🤖 ${this.agentType}: Generating structured response`);

    try {
      const { object } = await generateObject({
        model: getModel('complex'), // Use complex model for better tool support
        system: this.systemPrompt,
        prompt: `${prompt}\n\nContext: ${JSON.stringify(context, null, 2)}`,
        schema,
      });

      console.log(`✅ ${this.agentType}: Structured response generated successfully`);
      return object;
    } catch (error) {
      console.error(`❌ ${this.agentType}: Structured response generation failed:`, {
        error: error instanceof Error ? error.message : error,
        agentType: this.agentType,
        prompt: prompt.substring(0, 100) + '...',
      });

      // Re-throw with more context
      throw new Error(`${this.agentType} structured response failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  protected async generateTextResponse(
    prompt: string,
    context?: any
  ): Promise<string> {
    const { text } = await generateText({
      model: getModel('complex'),
      system: this.systemPrompt,
      prompt: `${prompt}\n\nContext: ${JSON.stringify(context, null, 2)}`,
    });

    return text;
  }

  protected async cacheData(
    projectId: string,
    sourceType: string,
    sourceUrl: string,
    data: any,
    expiresInHours: number = 24
  ): Promise<void> {
    const hash = this.generateHash(JSON.stringify(data));
    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + expiresInHours);

    try {
      await this.db.from('data_sources').upsert({
        project_id: projectId,
        source_type: sourceType,
        source_url: sourceUrl,
        data,
        hash,
        expires_at: expiresAt.toISOString(),
        last_updated: new Date().toISOString(),
      });
    } catch (error) {
      console.error(`Failed to cache data for ${sourceType}:`, error);
    }
  }

  protected async getCachedData(
    projectId: string,
    sourceType: string,
    sourceUrl: string
  ): Promise<any | null> {
    try {
      const { data } = await this.db
        .from('data_sources')
        .select('data, expires_at')
        .eq('project_id', projectId)
        .eq('source_type', sourceType)
        .eq('source_url', sourceUrl)
        .gte('expires_at', new Date().toISOString())
        .single();

      return data?.data || null;
    } catch (error) {
      return null;
    }
  }

  private generateHash(data: string): string {
    let hash = 0;
    for (let i = 0; i < data.length; i++) {
      const char = data.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return hash.toString();
  }

  protected async rateLimitWait(delayMs: number = 1000): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, delayMs));
  }

  protected validateInput(input: any, schema: z.ZodSchema): boolean {
    try {
      schema.parse(input);
      return true;
    } catch {
      return false;
    }
  }
}