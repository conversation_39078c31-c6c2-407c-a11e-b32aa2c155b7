import { z } from 'zod';
import { FirecrawlService } from './integrations/firecrawl';
import { ExaService } from './integrations/exa';
import { SocialMediaService } from './integrations/social-media';

const ProjectInputSchema = z.object({
  domain: z.string().url().optional(),
  twitter: z.string().optional(),
  telegram: z.string().optional(),
  discord: z.string().optional(),
  github: z.string().url().optional(),
  name: z.string().optional(),
  description: z.string().optional(),
});

export type ProjectInput = z.infer<typeof ProjectInputSchema>;

export interface DiscoveredProject {
  name: string;
  description: string;
  website?: string;
  type: string;
  social_handles: {
    twitter?: string;
    telegram?: string;
    discord?: string;
    github?: string;
  };
  metadata: {
    logo?: string;
    founded?: string;
    team_size?: number;
    funding?: string;
    blockchain?: string[];
    tokens?: string[];
  };
  confidence_score: number;
  discovery_method: string;
}

export class InputDiscoveryService {
  private firecrawl: FirecrawlService;
  private exa: ExaService;
  private socialMedia: SocialMediaService;

  constructor() {
    this.firecrawl = new FirecrawlService();
    this.exa = new ExaService();
    this.socialMedia = new SocialMediaService();
  }

  async processInput(input: string): Promise<DiscoveredProject> {
    const validatedInput = this.validateAndParseInput(input);
    
    if (validatedInput.domain) {
      return await this.discoverFromDomain(validatedInput.domain);
    }
    
    if (validatedInput.twitter) {
      return await this.discoverFromTwitter(validatedInput.twitter);
    }
    
    if (validatedInput.github) {
      return await this.discoverFromGithub(validatedInput.github);
    }
    
    // If no specific input type, try to discover from general search
    return await this.discoverFromSearch(input);
  }

  private validateAndParseInput(input: string): ProjectInput {
    // Try to detect input type
    const urlPattern = /^https?:\/\//;
    const twitterPattern = /^@?[\w]+$/;
    const domainPattern = /^[\w.-]+\.[a-zA-Z]{2,}$/;

    if (urlPattern.test(input)) {
      if (input.includes('twitter.com') || input.includes('x.com')) {
        const username = this.extractTwitterUsername(input);
        return { twitter: username };
      }
      if (input.includes('github.com')) {
        return { github: input };
      }
      if (input.includes('t.me') || input.includes('telegram')) {
        const username = this.extractTelegramUsername(input);
        return { telegram: username };
      }
      // Assume it's a domain
      return { domain: input };
    }

    if (twitterPattern.test(input)) {
      return { twitter: input.replace('@', '') };
    }

    if (domainPattern.test(input)) {
      return { domain: `https://${input}` };
    }

    // If none match, treat as project name
    return { name: input };
  }

  private extractTwitterUsername(url: string): string {
    const match = url.match(/(?:twitter\.com|x\.com)\/([^\/\?]+)/);
    return match ? match[1] : '';
  }

  private extractTelegramUsername(url: string): string {
    const match = url.match(/t\.me\/([^\/\?]+)/);
    return match ? match[1] : '';
  }

  async discoverFromDomain(domain: string): Promise<DiscoveredProject> {
    try {
      // Scrape the website
      const websiteData = await this.firecrawl.scrapeUrl(domain);
      
      if (!websiteData.success) {
        throw new Error('Failed to scrape website');
      }

      const content = websiteData.data.markdown || '';
      const metadata = websiteData.data.metadata || {};

      // Extract project information from content
      const projectInfo = await this.extractProjectInfoFromContent(content, metadata);
      
      // Find social handles
      const socialHandles = this.extractSocialHandles(content, domain);
      
      // Use Exa to find additional information
      const exaResults = await this.exa.searchWeb(`${projectInfo.name} crypto project`, {
        numResults: 5,
        maxCharacters: 1000,
      });

      // Enhance information with search results
      const enhancedInfo = await this.enhanceProjectInfo(projectInfo, exaResults.results || []);

      return {
        name: projectInfo.name || this.extractDomainName(domain),
        description: projectInfo.description || metadata.description || '',
        website: domain,
        type: await this.detectProjectType(projectInfo, content),
        social_handles: socialHandles,
        metadata: {
          logo: metadata.image || projectInfo.logo,
          founded: projectInfo.founded,
          team_size: projectInfo.team_size,
          funding: projectInfo.funding,
          blockchain: projectInfo.blockchains || [],
          tokens: projectInfo.tokens || [],
        },
        confidence_score: this.calculateConfidenceScore(projectInfo, socialHandles, content),
        discovery_method: 'domain_scraping',
      };

    } catch (error) {
      console.error('Domain discovery error:', error);
      return this.createFallbackProject(domain);
    }
  }

  async discoverFromTwitter(username: string): Promise<DiscoveredProject> {
    try {
      // Get Twitter profile
      const twitterProfile = await this.socialMedia.getTwitterProfile(username);
      
      if (!twitterProfile.success) {
        throw new Error('Failed to get Twitter profile');
      }

      const profile = twitterProfile.data;
      
      // Extract project information from Twitter bio and tweets
      const tweets = await this.socialMedia.getTwitterTweets(username, 20);
      const projectInfo = await this.extractProjectInfoFromTwitter(profile, tweets.tweets || []);
      
      // Search for more information using Exa
      const searchQuery = `${profile.name} crypto blockchain project`;
      const exaResults = await this.exa.searchWeb(searchQuery, {
        numResults: 5,
        maxCharacters: 1000,
      });

      // Try to find website from bio or tweets
      const website = this.extractWebsiteFromTwitter(profile, tweets.tweets || []);
      
      return {
        name: projectInfo.name || profile.name || username,
        description: projectInfo.description || profile.description || '',
        website: website,
        type: await this.detectProjectType(projectInfo, profile.description || ''),
        social_handles: {
          twitter: username,
          telegram: projectInfo.telegram,
          discord: projectInfo.discord,
          github: projectInfo.github,
        },
        metadata: {
          logo: profile.profile_image_url,
          founded: projectInfo.founded,
          team_size: projectInfo.team_size,
          funding: projectInfo.funding,
          blockchain: projectInfo.blockchains || [],
          tokens: projectInfo.tokens || [],
        },
        confidence_score: this.calculateConfidenceScore(projectInfo, { twitter: username }, profile.description || ''),
        discovery_method: 'twitter_analysis',
      };

    } catch (error) {
      console.error('Twitter discovery error:', error);
      return this.createFallbackProject(`@${username}`);
    }
  }

  async discoverFromGithub(githubUrl: string): Promise<DiscoveredProject> {
    try {
      // Scrape GitHub repository
      const githubData = await this.firecrawl.scrapeUrl(githubUrl);
      
      if (!githubData.success) {
        throw new Error('Failed to scrape GitHub repository');
      }

      const content = githubData.data.markdown || '';
      const metadata = githubData.data.metadata || {};

      // Extract project information
      const projectInfo = await this.extractProjectInfoFromGithub(content, metadata, githubUrl);
      
      // Search for additional information
      const exaResults = await this.exa.searchWeb(`${projectInfo.name} crypto project`, {
        numResults: 5,
        maxCharacters: 1000,
      });

      return {
        name: projectInfo.name || this.extractRepoName(githubUrl),
        description: projectInfo.description || metadata.description || '',
        website: projectInfo.website,
        type: await this.detectProjectType(projectInfo, content),
        social_handles: {
          github: githubUrl,
          twitter: projectInfo.twitter,
          telegram: projectInfo.telegram,
          discord: projectInfo.discord,
        },
        metadata: {
          logo: projectInfo.logo,
          founded: projectInfo.founded,
          team_size: projectInfo.team_size,
          funding: projectInfo.funding,
          blockchain: projectInfo.blockchains || [],
          tokens: projectInfo.tokens || [],
        },
        confidence_score: this.calculateConfidenceScore(projectInfo, { github: githubUrl }, content),
        discovery_method: 'github_analysis',
      };

    } catch (error) {
      console.error('GitHub discovery error:', error);
      return this.createFallbackProject(githubUrl);
    }
  }

  async discoverFromSearch(query: string): Promise<DiscoveredProject> {
    try {
      // Use Exa to search for the project
      const searchResults = await this.exa.searchWeb(`${query} crypto blockchain project`, {
        numResults: 10,
        maxCharacters: 1500,
      });

      if (!searchResults.success || !searchResults.results?.length) {
        throw new Error('No search results found');
      }

      // Analyze search results to extract project information
      const projectInfo = await this.extractProjectInfoFromSearchResults(searchResults.results, query);
      
      // Try to find official website
      const website = this.findOfficialWebsite(searchResults.results);
      
      return {
        name: projectInfo.name || query,
        description: projectInfo.description || '',
        website: website,
        type: await this.detectProjectType(projectInfo, projectInfo.description || ''),
        social_handles: {
          twitter: projectInfo.twitter,
          telegram: projectInfo.telegram,
          discord: projectInfo.discord,
          github: projectInfo.github,
        },
        metadata: {
          logo: projectInfo.logo,
          founded: projectInfo.founded,
          team_size: projectInfo.team_size,
          funding: projectInfo.funding,
          blockchain: projectInfo.blockchains || [],
          tokens: projectInfo.tokens || [],
        },
        confidence_score: this.calculateConfidenceScore(projectInfo, {}, projectInfo.description || ''),
        discovery_method: 'search_analysis',
      };

    } catch (error) {
      console.error('Search discovery error:', error);
      return this.createFallbackProject(query);
    }
  }

  private async extractProjectInfoFromContent(content: string, metadata: any): Promise<any> {
    // Extract common project information patterns
    const info: any = {
      name: metadata.title || this.extractPattern(content, /# ([^\n]+)/),
      description: metadata.description || this.extractPattern(content, /## About\n([^\n]+)/),
      logo: metadata.image,
    };

    // Look for blockchain-related keywords
    info.blockchains = this.extractBlockchains(content);
    info.tokens = this.extractTokens(content);
    
    return info;
  }

  private extractSocialHandles(content: string, domain: string): any {
    const handles: any = {};
    
    // Twitter
    const twitterMatch = content.match(/(?:twitter\.com|x\.com)\/([^\s\)]+)/i);
    if (twitterMatch) {
      handles.twitter = twitterMatch[1];
    }
    
    // Telegram
    const telegramMatch = content.match(/t\.me\/([^\s\)]+)/i);
    if (telegramMatch) {
      handles.telegram = telegramMatch[1];
    }
    
    // Discord
    const discordMatch = content.match(/discord\.gg\/([^\s\)]+)/i);
    if (discordMatch) {
      handles.discord = discordMatch[1];
    }
    
    // GitHub
    const githubMatch = content.match(/github\.com\/([^\s\)]+)/i);
    if (githubMatch) {
      handles.github = `https://github.com/${githubMatch[1]}`;
    }
    
    return handles;
  }

  private async extractProjectInfoFromTwitter(profile: any, tweets: any[]): Promise<any> {
    const info: any = {
      name: profile.name,
      description: profile.description,
    };

    // Analyze tweets for project information
    const tweetText = tweets.map(t => t.text).join(' ');
    info.blockchains = this.extractBlockchains(tweetText);
    info.tokens = this.extractTokens(tweetText);
    
    // Look for social handles in bio and tweets
    const allText = profile.description + ' ' + tweetText;
    info.telegram = this.extractPattern(allText, /t\.me\/([^\s\)]+)/i);
    info.discord = this.extractPattern(allText, /discord\.gg\/([^\s\)]+)/i);
    info.github = this.extractPattern(allText, /github\.com\/([^\s\)]+)/i);
    
    return info;
  }

  private async extractProjectInfoFromGithub(content: string, metadata: any, githubUrl: string): Promise<any> {
    const info: any = {
      name: metadata.title || this.extractRepoName(githubUrl),
      description: metadata.description || this.extractPattern(content, /# ([^\n]+)/),
    };

    // Look for website in README
    info.website = this.extractPattern(content, /(?:website|homepage|site):\s*(https?:\/\/[^\s\)]+)/i);
    
    // Extract blockchain and token information
    info.blockchains = this.extractBlockchains(content);
    info.tokens = this.extractTokens(content);
    
    return info;
  }

  private async extractProjectInfoFromSearchResults(results: any[], query: string): Promise<any> {
    const allContent = results.map(r => r.content).join(' ');
    const allTitles = results.map(r => r.title).join(' ');
    
    const info: any = {
      name: query,
      description: this.extractDescription(allContent, query),
    };

    // Extract information from search results
    info.blockchains = this.extractBlockchains(allContent);
    info.tokens = this.extractTokens(allContent);
    
    // Look for social handles
    info.twitter = this.extractPattern(allContent, /(?:twitter\.com|x\.com)\/([^\s\)]+)/i);
    info.telegram = this.extractPattern(allContent, /t\.me\/([^\s\)]+)/i);
    info.discord = this.extractPattern(allContent, /discord\.gg\/([^\s\)]+)/i);
    info.github = this.extractPattern(allContent, /github\.com\/([^\s\)]+)/i);
    
    return info;
  }

  private extractPattern(text: string, pattern: RegExp): string | undefined {
    const match = text.match(pattern);
    return match ? match[1] : undefined;
  }

  private extractBlockchains(content: string): string[] {
    const blockchains = ['ethereum', 'bitcoin', 'polygon', 'binance', 'avalanche', 'solana', 'arbitrum', 'optimism'];
    return blockchains.filter(chain => content.toLowerCase().includes(chain));
  }

  private extractTokens(content: string): string[] {
    const tokenPattern = /\$([A-Z]{2,10})/g;
    const matches = content.match(tokenPattern);
    return matches ? [...new Set(matches.map(m => m.slice(1)))] : [];
  }

  private extractDescription(content: string, projectName: string): string {
    // Try to find a sentence that describes the project
    const sentences = content.split(/[.!?]/);
    for (const sentence of sentences) {
      if (sentence.toLowerCase().includes(projectName.toLowerCase()) && 
          (sentence.includes('protocol') || sentence.includes('platform') || sentence.includes('project'))) {
        return sentence.trim();
      }
    }
    return '';
  }

  private extractDomainName(domain: string): string {
    const url = new URL(domain);
    return url.hostname.replace('www.', '').split('.')[0];
  }

  private extractRepoName(githubUrl: string): string {
    const match = githubUrl.match(/github\.com\/[^\/]+\/([^\/]+)/);
    return match ? match[1] : '';
  }

  private extractWebsiteFromTwitter(profile: any, tweets: any[]): string | undefined {
    // Check profile URL first
    if (profile.url && !profile.url.includes('twitter.com') && !profile.url.includes('x.com')) {
      return profile.url;
    }
    
    // Look for website links in recent tweets
    const urlPattern = /https?:\/\/[^\s\)]+/g;
    for (const tweet of tweets) {
      const urls = tweet.text.match(urlPattern);
      if (urls) {
        for (const url of urls) {
          if (!url.includes('twitter.com') && !url.includes('x.com') && 
              !url.includes('t.me') && !url.includes('discord')) {
            return url;
          }
        }
      }
    }
    
    return undefined;
  }

  private findOfficialWebsite(searchResults: any[]): string | undefined {
    // Look for official website in search results
    for (const result of searchResults) {
      if (result.url && !result.url.includes('twitter.com') && 
          !result.url.includes('github.com') && !result.url.includes('reddit.com') &&
          !result.url.includes('medium.com') && !result.url.includes('telegram.me')) {
        return result.url;
      }
    }
    return undefined;
  }

  private async detectProjectType(projectInfo: any, content: string): Promise<string> {
    const contentLower = content.toLowerCase();
    
    if (contentLower.includes('defi') || contentLower.includes('decentralized finance')) return 'defi';
    if (contentLower.includes('nft') || contentLower.includes('non-fungible')) return 'nft';
    if (contentLower.includes('gaming') || contentLower.includes('game')) return 'gaming';
    if (contentLower.includes('dao') || contentLower.includes('governance')) return 'dao';
    if (contentLower.includes('infrastructure') || contentLower.includes('blockchain')) return 'infrastructure';
    if (contentLower.includes('exchange') || contentLower.includes('trading')) return 'exchange';
    if (contentLower.includes('wallet')) return 'wallet';
    
    return 'protocol';
  }

  private calculateConfidenceScore(projectInfo: any, socialHandles: any, content: string): number {
    let score = 0;
    
    // Basic information
    if (projectInfo.name) score += 20;
    if (projectInfo.description) score += 20;
    
    // Social presence
    if (socialHandles.twitter) score += 15;
    if (socialHandles.telegram) score += 10;
    if (socialHandles.discord) score += 10;
    if (socialHandles.github) score += 15;
    
    // Technical indicators
    if (projectInfo.blockchains?.length > 0) score += 10;
    if (projectInfo.tokens?.length > 0) score += 5;
    
    // Content quality
    if (content.length > 500) score += 5;
    
    return Math.min(score, 100);
  }

  private createFallbackProject(input: string): DiscoveredProject {
    return {
      name: input,
      description: `Project discovered from input: ${input}`,
      type: 'unknown',
      social_handles: {},
      metadata: {},
      confidence_score: 10,
      discovery_method: 'fallback',
    };
  }

  private async enhanceProjectInfo(projectInfo: any, searchResults: any[]): Promise<any> {
    // Enhance project information with search results
    if (!projectInfo.description && searchResults.length > 0) {
      projectInfo.description = this.extractDescription(
        searchResults.map(r => r.content).join(' '), 
        projectInfo.name
      );
    }
    
    return projectInfo;
  }
}