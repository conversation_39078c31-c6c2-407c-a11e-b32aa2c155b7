import { z } from 'zod';

export const ProjectInputSchema = z.object({
  type: z.enum(['domain', 'twitter', 'contract']),
  value: z.string(),
  reportDepth: z.enum(['quick', 'standard', 'deep']).default('standard'),
});

export type ProjectInput = z.infer<typeof ProjectInputSchema>;

export const AgentResultSchema = z.object({
  agentType: z.string(),
  status: z.enum(['pending', 'running', 'completed', 'failed']),
  data: z.any().optional(),
  error: z.string().optional(),
  startTime: z.date(),
  endTime: z.date().optional(),
  metrics: z.record(z.any()).optional(),
});

export type AgentResult = z.infer<typeof AgentResultSchema>;

export const ReportSectionSchema = z.object({
  title: z.string(),
  content: z.string(),
  charts: z.array(z.any()).optional(),
  sources: z.array(z.string()).optional(),
});

export type ReportSection = z.infer<typeof ReportSectionSchema>;