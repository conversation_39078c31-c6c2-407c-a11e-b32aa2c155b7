export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          full_name: string | null
          avatar_url: string | null
          workspace_id: string | null
          role: 'admin' | 'member' | 'viewer'
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          email: string
          full_name?: string | null
          avatar_url?: string | null
          workspace_id?: string | null
          role?: 'admin' | 'member' | 'viewer'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string | null
          avatar_url?: string | null
          workspace_id?: string | null
          role?: 'admin' | 'member' | 'viewer'
          created_at?: string
          updated_at?: string
        }
      }
      projects: {
        Row: {
          id: string
          name: string
          type: 'domain' | 'twitter' | 'contract'
          identifier: string
          description: string | null
          website: string | null
          twitter_handle: string | null
          github_url: string | null
          contract_addresses: Json | null
          metadata: Json | null
          created_by: string
          workspace_id: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          type: 'domain' | 'twitter' | 'contract'
          identifier: string
          description?: string | null
          website?: string | null
          twitter_handle?: string | null
          github_url?: string | null
          contract_addresses?: Json | null
          metadata?: Json | null
          created_by: string
          workspace_id: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          type?: 'domain' | 'twitter' | 'contract'
          identifier?: string
          description?: string | null
          website?: string | null
          twitter_handle?: string | null
          github_url?: string | null
          contract_addresses?: Json | null
          metadata?: Json | null
          created_by?: string
          workspace_id?: string
          created_at?: string
          updated_at?: string
        }
      }
      reports: {
        Row: {
          id: string
          project_id: string
          title: string
          status: 'pending' | 'generating' | 'completed' | 'failed'
          depth: 'quick' | 'standard' | 'deep'
          content: Json | null
          executive_summary: string | null
          market_analysis: Json | null
          competitive_landscape: Json | null
          technical_analysis: Json | null
          tokenomics_analysis: Json | null
          growth_metrics: Json | null
          recommendations: Json | null
          sources: Json | null
          generated_by: string
          workspace_id: string
          created_at: string
          updated_at: string
          completed_at: string | null
        }
        Insert: {
          id?: string
          project_id: string
          title: string
          status?: 'pending' | 'generating' | 'completed' | 'failed'
          depth?: 'quick' | 'standard' | 'deep'
          content?: Json | null
          executive_summary?: string | null
          market_analysis?: Json | null
          competitive_landscape?: Json | null
          technical_analysis?: Json | null
          tokenomics_analysis?: Json | null
          growth_metrics?: Json | null
          recommendations?: Json | null
          sources?: Json | null
          generated_by: string
          workspace_id: string
          created_at?: string
          updated_at?: string
          completed_at?: string | null
        }
        Update: {
          id?: string
          project_id?: string
          title?: string
          status?: 'pending' | 'generating' | 'completed' | 'failed'
          depth?: 'quick' | 'standard' | 'deep'
          content?: Json | null
          executive_summary?: string | null
          market_analysis?: Json | null
          competitive_landscape?: Json | null
          technical_analysis?: Json | null
          tokenomics_analysis?: Json | null
          growth_metrics?: Json | null
          recommendations?: Json | null
          sources?: Json | null
          generated_by?: string
          workspace_id?: string
          created_at?: string
          updated_at?: string
          completed_at?: string | null
        }
      }
      agent_runs: {
        Row: {
          id: string
          report_id: string
          agent_type: string
          status: 'pending' | 'running' | 'completed' | 'failed'
          input_data: Json | null
          output_data: Json | null
          error_message: string | null
          metrics: Json | null
          started_at: string | null
          completed_at: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          report_id: string
          agent_type: string
          status?: 'pending' | 'running' | 'completed' | 'failed'
          input_data?: Json | null
          output_data?: Json | null
          error_message?: string | null
          metrics?: Json | null
          started_at?: string | null
          completed_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          report_id?: string
          agent_type?: string
          status?: 'pending' | 'running' | 'completed' | 'failed'
          input_data?: Json | null
          output_data?: Json | null
          error_message?: string | null
          metrics?: Json | null
          started_at?: string | null
          completed_at?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      data_sources: {
        Row: {
          id: string
          project_id: string
          source_type: string
          source_url: string
          data: Json
          hash: string
          confidence_score: number | null
          last_updated: string
          expires_at: string | null
          created_at: string
        }
        Insert: {
          id?: string
          project_id: string
          source_type: string
          source_url: string
          data: Json
          hash: string
          confidence_score?: number | null
          last_updated?: string
          expires_at?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          project_id?: string
          source_type?: string
          source_url?: string
          data?: Json
          hash?: string
          confidence_score?: number | null
          last_updated?: string
          expires_at?: string | null
          created_at?: string
        }
      }
      embeddings: {
        Row: {
          id: string
          content: string
          embedding: string
          metadata: Json | null
          project_id: string | null
          source_id: string | null
          created_at: string
        }
        Insert: {
          id?: string
          content: string
          embedding: string
          metadata?: Json | null
          project_id?: string | null
          source_id?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          content?: string
          embedding?: string
          metadata?: Json | null
          project_id?: string | null
          source_id?: string | null
          created_at?: string
        }
      }
      workspaces: {
        Row: {
          id: string
          name: string
          slug: string
          plan: 'free' | 'pro' | 'enterprise'
          settings: Json | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          slug: string
          plan?: 'free' | 'pro' | 'enterprise'
          settings?: Json | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          slug?: string
          plan?: 'free' | 'pro' | 'enterprise'
          settings?: Json | null
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      match_embeddings: {
        Args: {
          query_embedding: string
          similarity_threshold?: number
          match_count?: number
        }
        Returns: {
          id: string
          content: string
          similarity: number
          metadata: Json
        }[]
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}