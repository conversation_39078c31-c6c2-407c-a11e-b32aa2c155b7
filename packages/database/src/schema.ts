import { z } from 'zod';

// Project Input Schema
export const ProjectInputSchema = z.object({
  type: z.enum(['domain', 'twitter', 'contract']),
  value: z.string().min(1, 'Input value is required'),
  reportDepth: z.enum(['quick', 'standard', 'deep']).default('standard'),
});

export type ProjectInput = z.infer<typeof ProjectInputSchema>;

// Project Schema
export const ProjectSchema = z.object({
  id: z.string(),
  name: z.string(),
  type: z.enum(['domain', 'twitter', 'contract']),
  identifier: z.string(),
  description: z.string().nullable(),
  website: z.string().url().nullable(),
  twitterHandle: z.string().nullable(),
  githubUrl: z.string().url().nullable(),
  contractAddresses: z.any().nullable(),
  metadata: z.any().nullable(),
  createdBy: z.string(),
  workspaceId: z.string(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export type Project = z.infer<typeof ProjectSchema>;

// Report Schema
export const ReportSchema = z.object({
  id: z.string(),
  projectId: z.string(),
  title: z.string(),
  status: z.enum(['pending', 'generating', 'completed', 'failed']),
  depth: z.enum(['quick', 'standard', 'deep']),
  content: z.any().nullable(),
  executiveSummary: z.string().nullable(),
  marketAnalysis: z.any().nullable(),
  competitiveLandscape: z.any().nullable(),
  technicalAnalysis: z.any().nullable(),
  tokenomicsAnalysis: z.any().nullable(),
  growthMetrics: z.any().nullable(),
  recommendations: z.any().nullable(),
  sources: z.array(z.string()).nullable(),
  generatedBy: z.string(),
  workspaceId: z.string(),
  createdAt: z.date(),
  updatedAt: z.date(),
  completedAt: z.date().nullable(),
});

export type Report = z.infer<typeof ReportSchema>;

// Agent Run Schema
export const AgentRunSchema = z.object({
  id: z.string(),
  reportId: z.string(),
  agentType: z.string(),
  status: z.enum(['pending', 'running', 'completed', 'failed']),
  inputData: z.any().nullable(),
  outputData: z.any().nullable(),
  errorMessage: z.string().nullable(),
  metrics: z.any().nullable(),
  startedAt: z.date().nullable(),
  completedAt: z.date().nullable(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export type AgentRun = z.infer<typeof AgentRunSchema>;

// Data Source Schema
export const DataSourceSchema = z.object({
  id: z.string(),
  projectId: z.string(),
  sourceType: z.string(),
  sourceUrl: z.string(),
  data: z.any(),
  hash: z.string(),
  confidenceScore: z.number().nullable(),
  lastUpdated: z.date(),
  expiresAt: z.date().nullable(),
  createdAt: z.date(),
});

export type DataSource = z.infer<typeof DataSourceSchema>;

// Agent Result Schema (for runtime use)
export const AgentResultSchema = z.object({
  success: z.boolean(),
  data: z.any().optional(),
  error: z.string().optional(),
  sources: z.array(z.string()).optional(),
  metrics: z.record(z.any()).optional(),
  startTime: z.date(),
  endTime: z.date().optional(),
});

export type AgentResult = z.infer<typeof AgentResultSchema>;

// Agent Context Schema
export const AgentContextSchema = z.object({
  reportId: z.string(),
  projectId: z.string(),
  workspaceId: z.string(),
  userId: z.string(),
  reportDepth: z.enum(['quick', 'standard', 'deep']),
  projectData: z.any(),
});

export type AgentContext = z.infer<typeof AgentContextSchema>;

// Report Section Schema
export const ReportSectionSchema = z.object({
  title: z.string(),
  content: z.string(),
  charts: z.array(z.any()).optional(),
  sources: z.array(z.string()).optional(),
  confidence: z.number().min(0).max(1).optional(),
});

export type ReportSection = z.infer<typeof ReportSectionSchema>;

// Validation helpers
export const validateProjectInput = (input: unknown): ProjectInput => {
  return ProjectInputSchema.parse(input);
};

export const validateAgentResult = (result: unknown): AgentResult => {
  return AgentResultSchema.parse(result);
};

export const validateAgentContext = (context: unknown): AgentContext => {
  return AgentContextSchema.parse(context);
};

// Database operation schemas
export const CreateProjectSchema = ProjectSchema.omit({ 
  id: true, 
  createdAt: true, 
  updatedAt: true 
});

export const UpdateProjectSchema = CreateProjectSchema.partial();

export const CreateReportSchema = ReportSchema.omit({ 
  id: true, 
  createdAt: true, 
  updatedAt: true,
  completedAt: true 
});

export const UpdateReportSchema = CreateReportSchema.partial();

export type CreateProject = z.infer<typeof CreateProjectSchema>;
export type UpdateProject = z.infer<typeof UpdateProjectSchema>;
export type CreateReport = z.infer<typeof CreateReportSchema>;
export type UpdateReport = z.infer<typeof UpdateReportSchema>;
