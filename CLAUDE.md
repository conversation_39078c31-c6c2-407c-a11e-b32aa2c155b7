# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 🚀 Development Commands

```bash
# Start all applications in development mode
bun dev

# Start specific applications
bun dev:web          # Frontend only (port 3001)
bun dev:server       # Backend API only (port 3000)

# Database operations
bun db:push          # Push schema changes to database
bun db:studio        # Open Prisma database studio UI
bun db:generate      # Generate Prisma client
bun db:migrate       # Run database migrations

# Build and type checking
bun build            # Build all applications
bun check-types      # TypeScript type checking across monorepo
```

## 🏗️ Architecture Overview

This is a **CMA (Crypto Market Analysis)** platform that generates AI-powered research reports for crypto projects. The system analyzes domains, Twitter accounts, and smart contracts.

### Core Components

- **Monorepo Structure**: Turborepo with `apps/web` (Next.js frontend) and `apps/server` (Next.js API backend)
- **Database**: PostgreSQL with Prisma ORM, includes pgvector for embeddings
- **API**: tRPC for type-safe API calls between frontend and backend
- **AI Processing**: Agent orchestration system for multi-step analysis

### Data Flow

1. **Input**: User submits domain/Twitter/contract for analysis
2. **Project Creation**: System creates/finds project in database
3. **Report Generation**: Creates report record with PENDING status
4. **Agent Orchestration**: Multiple AI agents run analysis in background
5. **Progress Tracking**: Real-time updates via AgentRun records
6. **Report Completion**: Final analysis compiled into structured report

### Key Models

- **Workspace**: Multi-tenant organization structure
- **Project**: The entity being analyzed (domain/Twitter/contract)
- **Report**: Generated analysis with sections (market, technical, tokenomics)
- **AgentRun**: Individual AI agent execution tracking
- **DataSource**: Cached external data with expiration
- **Embedding**: Vector embeddings for semantic search

## 🔧 Tech Stack Details

- **Frontend**: Next.js 15 with React 19, TailwindCSS, shadcn/ui components
- **Backend**: Next.js API routes with tRPC, Prisma client
- **Database**: PostgreSQL with pgvector extension for embeddings
- **AI/ML**: OpenAI SDK, Firecrawl for web scraping, Exa for search
- **Package Manager**: Bun with workspaces
- **Development**: Turbopack for fast dev builds

## 📁 Key File Locations

- **API Routes**: `apps/server/src/routers/index.ts` - Main tRPC router
- **Database Schema**: `apps/server/prisma/schema/schema.prisma`
- **Frontend Pages**: `apps/web/src/app/` - Next.js app router structure
- **Shared Types**: Import from `@cma/` packages (agents, ai, database)

## 🔑 Environment Setup

The server requires PostgreSQL connection:
- `DATABASE_URL` - Primary database connection
- `DIRECT_URL` - Direct database connection for migrations

AI services require:
- OpenAI API keys for analysis
- Firecrawl for web scraping
- Exa for enhanced search capabilities

## 🚦 Development Workflow

1. **Database First**: Run `bun db:push` after schema changes
2. **Type Safety**: Use `bun check-types` before commits
3. **Parallel Development**: Frontend (3001) and backend (3000) run simultaneously
4. **Agent Testing**: Monitor AgentRun status for debugging AI processes